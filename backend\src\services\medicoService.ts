import { PrismaClient } from '@prisma/client'
import { AuthService } from './authService'
import { 
  CreateMedicoDTO, 
  UpdateMedicoDTO, 
  MedicoFilters,
  TipoUsuario,
  StatusUsuario 
} from '../../../shared/src/types'
import { validarCRM } from '../../../shared/src/utils'

const prisma = new PrismaClient()
const authService = new AuthService()

export class MedicoService {
  /**
   * Criar novo médico
   */
  async create(data: CreateMedicoDTO) {
    const { usuario, crm, biografia, endereco, especialidades } = data

    // Validar CRM
    if (!validarCRM(crm)) {
      throw new Error('CRM inválido')
    }

    // Verificar se CRM já existe
    const crmExistente = await prisma.medico.findUnique({
      where: { crm }
    })

    if (crmExistente) {
      throw new Error('CRM já está em uso')
    }

    // Verificar se especialidades existem
    if (especialidades && especialidades.length > 0) {
      const especialidadesExistentes = await prisma.especialidade.findMany({
        where: { id: { in: especialidades } }
      })

      if (especialidadesExistentes.length !== especialidades.length) {
        throw new Error('Uma ou mais especialidades não foram encontradas')
      }
    }

    try {
      // Usar transação para criar usuário e médico
      const resultado = await prisma.$transaction(async (tx) => {
        // Criar usuário
        const novoUsuario = await authService.register({
          ...usuario,
          tipo: TipoUsuario.MEDICO
        })

        // Criar médico
        const novoMedico = await tx.medico.create({
          data: {
            usuarioId: novoUsuario.usuario.id,
            crm,
            biografia,
            endereco
          },
          include: {
            usuario: {
              select: {
                id: true,
                email: true,
                nome: true,
                telefone: true,
                tipo: true,
                status: true,
                criadoEm: true,
                atualizadoEm: true
              }
            },
            especialidades: {
              include: {
                especialidade: true
              }
            }
          }
        })

        // Associar especialidades se fornecidas
        if (especialidades && especialidades.length > 0) {
          await tx.medicoEspecialidade.createMany({
            data: especialidades.map(especialidadeId => ({
              medicoId: novoMedico.id,
              especialidadeId
            }))
          })
        }

        return novoMedico
      })

      // Buscar médico completo com especialidades
      return this.findById(resultado.id)
    } catch (error) {
      console.error('Erro ao criar médico:', error)
      throw new Error('Erro ao criar médico')
    }
  }

  /**
   * Buscar médico por ID
   */
  async findById(id: string) {
    const medico = await prisma.medico.findUnique({
      where: { id },
      include: {
        usuario: {
          select: {
            id: true,
            email: true,
            nome: true,
            telefone: true,
            tipo: true,
            status: true,
            criadoEm: true,
            atualizadoEm: true
          }
        },
        especialidades: {
          include: {
            especialidade: true
          }
        },
        horarios: {
          where: { ativo: true },
          orderBy: [
            { diaSemana: 'asc' },
            { horaInicio: 'asc' }
          ]
        },
        consultas: {
          take: 10,
          orderBy: { dataHora: 'desc' },
          include: {
            paciente: {
              include: {
                usuario: {
                  select: {
                    nome: true,
                    telefone: true
                  }
                }
              }
            },
            especialidade: {
              select: {
                nome: true
              }
            }
          }
        }
      }
    })

    if (!medico) {
      throw new Error('Médico não encontrado')
    }

    return medico
  }

  /**
   * Buscar médico por usuário ID
   */
  async findByUserId(usuarioId: string) {
    const medico = await prisma.medico.findUnique({
      where: { usuarioId },
      include: {
        usuario: {
          select: {
            id: true,
            email: true,
            nome: true,
            telefone: true,
            tipo: true,
            status: true,
            criadoEm: true,
            atualizadoEm: true
          }
        },
        especialidades: {
          include: {
            especialidade: true
          }
        },
        horarios: {
          where: { ativo: true },
          orderBy: [
            { diaSemana: 'asc' },
            { horaInicio: 'asc' }
          ]
        }
      }
    })

    if (!medico) {
      throw new Error('Médico não encontrado')
    }

    return medico
  }

  /**
   * Listar médicos com filtros e paginação
   */
  async findMany(filters: MedicoFilters = {}) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'nome',
      sortOrder = 'asc',
      especialidadeId,
      status,
      nome
    } = filters

    const skip = (page - 1) * limit

    // Construir where clause
    const where: any = {}

    if (especialidadeId) {
      where.especialidades = {
        some: {
          especialidadeId
        }
      }
    }

    if (status) {
      where.usuario = {
        status
      }
    }

    if (nome) {
      where.usuario = {
        ...where.usuario,
        nome: {
          contains: nome,
          mode: 'insensitive'
        }
      }
    }

    // Buscar médicos
    const [medicos, total] = await Promise.all([
      prisma.medico.findMany({
        where,
        include: {
          usuario: {
            select: {
              id: true,
              email: true,
              nome: true,
              telefone: true,
              tipo: true,
              status: true,
              criadoEm: true,
              atualizadoEm: true
            }
          },
          especialidades: {
            include: {
              especialidade: true
            }
          },
          _count: {
            select: {
              consultas: true
            }
          }
        },
        orderBy: sortBy === 'nome' ? {
          usuario: { nome: sortOrder }
        } : {
          [sortBy]: sortOrder
        },
        skip,
        take: limit
      }),
      prisma.medico.count({ where })
    ])

    return {
      data: medicos,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * Atualizar médico
   */
  async update(id: string, data: UpdateMedicoDTO) {
    const { biografia, endereco, especialidades } = data

    // Verificar se médico existe
    const medicoExistente = await this.findById(id)

    try {
      const resultado = await prisma.$transaction(async (tx) => {
        // Atualizar dados do médico
        const medicoAtualizado = await tx.medico.update({
          where: { id },
          data: {
            biografia,
            endereco
          }
        })

        // Atualizar especialidades se fornecidas
        if (especialidades) {
          // Remover especialidades existentes
          await tx.medicoEspecialidade.deleteMany({
            where: { medicoId: id }
          })

          // Adicionar novas especialidades
          if (especialidades.length > 0) {
            await tx.medicoEspecialidade.createMany({
              data: especialidades.map(especialidadeId => ({
                medicoId: id,
                especialidadeId
              }))
            })
          }
        }

        return medicoAtualizado
      })

      // Retornar médico atualizado completo
      return this.findById(id)
    } catch (error) {
      console.error('Erro ao atualizar médico:', error)
      throw new Error('Erro ao atualizar médico')
    }
  }

  /**
   * Desativar médico (soft delete)
   */
  async deactivate(id: string) {
    const medico = await this.findById(id)

    await prisma.usuario.update({
      where: { id: medico.usuarioId },
      data: { status: StatusUsuario.INATIVO }
    })

    return { message: 'Médico desativado com sucesso' }
  }

  /**
   * Ativar médico
   */
  async activate(id: string) {
    const medico = await this.findById(id)

    await prisma.usuario.update({
      where: { id: medico.usuarioId },
      data: { status: StatusUsuario.ATIVO }
    })

    return { message: 'Médico ativado com sucesso' }
  }

  /**
   * Buscar médicos por especialidade
   */
  async findByEspecialidade(especialidadeId: string) {
    return prisma.medico.findMany({
      where: {
        especialidades: {
          some: {
            especialidadeId
          }
        },
        usuario: {
          status: StatusUsuario.ATIVO
        }
      },
      include: {
        usuario: {
          select: {
            id: true,
            nome: true,
            telefone: true
          }
        },
        especialidades: {
          include: {
            especialidade: true
          }
        },
        horarios: {
          where: { ativo: true }
        }
      }
    })
  }

  /**
   * Obter estatísticas do médico
   */
  async getStats(id: string) {
    const medico = await this.findById(id)

    const [
      totalConsultas,
      consultasHoje,
      consultasSemana,
      consultasMes,
      proximasConsultas
    ] = await Promise.all([
      // Total de consultas
      prisma.consulta.count({
        where: { medicoId: id }
      }),
      // Consultas hoje
      prisma.consulta.count({
        where: {
          medicoId: id,
          dataHora: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lt: new Date(new Date().setHours(23, 59, 59, 999))
          }
        }
      }),
      // Consultas esta semana
      prisma.consulta.count({
        where: {
          medicoId: id,
          dataHora: {
            gte: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())),
            lt: new Date()
          }
        }
      }),
      // Consultas este mês
      prisma.consulta.count({
        where: {
          medicoId: id,
          dataHora: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            lt: new Date()
          }
        }
      }),
      // Próximas consultas
      prisma.consulta.findMany({
        where: {
          medicoId: id,
          dataHora: {
            gte: new Date()
          }
        },
        take: 5,
        orderBy: { dataHora: 'asc' },
        include: {
          paciente: {
            include: {
              usuario: {
                select: {
                  nome: true
                }
              }
            }
          },
          especialidade: {
            select: {
              nome: true
            }
          }
        }
      })
    ])

    return {
      totalConsultas,
      consultasHoje,
      consultasSemana,
      consultasMes,
      proximasConsultas
    }
  }
}
