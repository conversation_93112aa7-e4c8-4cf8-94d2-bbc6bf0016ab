# Requisitos Funcionais - Sistema de Agência Médica

## 1. Módulo de Autenticação e Autorização

### RF001 - Login de Usuário
- **Descrição**: O sistema deve permitir login com email e senha
- **Atores**: Todos os usuários
- **Critérios de Aceitação**:
  - Validação de credenciais
  - Redirecionamento baseado no perfil
  - Mensagens de erro claras

### RF002 - Controle de Acesso por Perfil
- **Descrição**: Diferentes níveis de acesso baseados no perfil do usuário
- **Perfis**: Administrador, Médico, Recepcionista, Paciente
- **Critérios de Aceitação**:
  - Restrição de funcionalidades por perfil
  - Proteção de rotas sensíveis

## 2. Módulo de Gestão de Médicos

### RF003 - Cadastro de Médicos
- **Descrição**: Cadastrar novos médicos no sistema
- **Campos Obrigatórios**:
  - Nome completo
  - CRM
  - Especialidade(s)
  - Email
  - Telefone
- **Campos Opcionais**:
  - Foto
  - Endereço
  - Biografia
  - Documentos

### RF004 - Gestão de Especialidades
- **Descrição**: Gerenciar especialidades médicas
- **Funcionalidades**:
  - Criar, editar, excluir especialidades
  - Associar médicos às especialidades
  - Definir valores de consulta por especialidade

### RF005 - Configuração de Horários
- **Descrição**: Definir horários de atendimento dos médicos
- **Funcionalidades**:
  - Horários por dia da semana
  - Intervalos de atendimento
  - Bloqueios e exceções

## 3. Módulo de Gestão de Pacientes

### RF006 - Cadastro de Pacientes
- **Descrição**: Cadastrar novos pacientes
- **Campos Obrigatórios**:
  - Nome completo
  - CPF
  - Data de nascimento
  - Telefone
- **Campos Opcionais**:
  - Email
  - Endereço
  - Plano de saúde
  - Contato de emergência

### RF007 - Histórico Médico
- **Descrição**: Manter histórico médico dos pacientes
- **Funcionalidades**:
  - Registro de consultas
  - Anexar exames
  - Prescrições médicas
  - Observações clínicas

## 4. Módulo de Agendamentos

### RF008 - Agendamento de Consultas
- **Descrição**: Agendar consultas médicas
- **Funcionalidades**:
  - Seleção de médico e especialidade
  - Verificação de disponibilidade
  - Confirmação automática
  - Envio de notificações

### RF009 - Calendário de Consultas
- **Descrição**: Visualizar agenda de consultas
- **Visualizações**:
  - Diária
  - Semanal
  - Mensal
- **Filtros**:
  - Por médico
  - Por especialidade
  - Por status

### RF010 - Gestão de Status de Consultas
- **Descrição**: Controlar status das consultas
- **Status Possíveis**:
  - Agendada
  - Confirmada
  - Em andamento
  - Concluída
  - Cancelada
  - Faltou

## 5. Módulo de Relatórios

### RF011 - Relatório de Atendimentos
- **Descrição**: Gerar relatórios de atendimentos
- **Filtros**:
  - Período
  - Médico
  - Especialidade
  - Status

### RF012 - Dashboard Administrativo
- **Descrição**: Painel com métricas do sistema
- **Métricas**:
  - Consultas do dia/semana/mês
  - Taxa de ocupação
  - Receita gerada
  - Pacientes atendidos

## 6. Módulo de Notificações

### RF013 - Notificações de Agendamento
- **Descrição**: Enviar notificações sobre consultas
- **Tipos**:
  - Confirmação de agendamento
  - Lembrete 24h antes
  - Lembrete 1h antes
  - Cancelamento

### RF014 - Notificações por Email/SMS
- **Descrição**: Envio de notificações por múltiplos canais
- **Canais**:
  - Email
  - SMS
  - Notificação no sistema

## 7. Módulo de Configurações

### RF015 - Configurações Gerais
- **Descrição**: Configurar parâmetros do sistema
- **Configurações**:
  - Dados da clínica
  - Horário de funcionamento
  - Valores de consulta
  - Templates de notificação

### RF016 - Backup e Restauração
- **Descrição**: Funcionalidades de backup
- **Funcionalidades**:
  - Backup automático
  - Backup manual
  - Restauração de dados
  - Histórico de backups
