{"name": "Triagem WhatsApp - <PERSON><PERSON><PERSON>", "nodes": [{"parameters": {"httpMethod": "POST", "path": "whatsapp-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-whatsapp", "name": "Webhook WhatsApp", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "verificar-mensagem", "leftValue": "={{ $json.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.text?.body }}", "rightValue": "", "operator": {"type": "string", "operation": "exists"}}], "combinator": "and"}, "options": {}}, "id": "verificar-mensagem", "name": "Verificar Mensagem", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// Extrair dados da mensagem do WhatsApp\nconst entry = $input.all()[0].json.entry[0];\nconst change = entry.changes[0];\nconst message = change.value.messages[0];\n\nconst userPhone = message.from;\nconst messageText = message.text.body;\nconst messageId = message.id;\nconst timestamp = message.timestamp;\n\n// Verificar se é uma mensagem de texto\nif (!messageText) {\n  return [{\n    json: {\n      error: 'Mensagem não é texto',\n      phone: userPhone\n    }\n  }];\n}\n\nreturn [{\n  json: {\n    phone: userPhone,\n    message: messageText,\n    messageId: messageId,\n    timestamp: timestamp,\n    businessPhoneId: change.value.metadata.phone_number_id\n  }\n}];"}, "id": "extrair-dados", "name": "Extrair Dados WhatsApp", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT p.id, u.nome, p.cpf, p.dataNascimento FROM pacientes p JOIN usuarios u ON p.usuarioId = u.id WHERE u.telefone LIKE '%{{ $json.phone.slice(-11) }}%' LIMIT 1"}, "id": "buscar-paciente", "name": "Buscar Paciente", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Você é Dr. IA, assistente virtual da Agenteia Médicos. Sua função é:\n\n1. TRIAGEM MÉDICA: Analisar sintomas e orientar sobre urgência\n2. AGENDAMENTO: Ajudar a agendar consultas\n3. INFORMAÇÕES: Responder dúvidas sobre a clínica\n\nDIRETRIZES IMPORTANTES:\n- Seja empático e profissional\n- NUNCA dê diagnósticos ou prescreva medicamentos\n- SEMPRE recomende consulta médica presencial\n- Para emergências, oriente procurar pronto-socorro\n- Mantenha respostas concisas (máximo 160 caracteres)\n\nCLASSIFICAÇÃO DE URGÊNCIA:\n🔴 EMERGÊNCIA: Do<PERSON> no peito, falta de ar severa, sangramento intenso\n🟡 URGENTE: Febre alta, dor intensa, vômitos persistentes\n🟢 NORMAL: Sintomas leves, consultas de rotina\n\nSe identificar sintomas, classifique a urgência e sugira próximos passos."}, {"role": "user", "content": "=Paciente: {{ $('buscar-paciente').item.json?.nome || 'Novo paciente' }}\nTelefone: {{ $('extrair-dados').item.json.phone }}\nMensagem: {{ $('extrair-dados').item.json.message }}"}]}}, "id": "ia-resposta", "name": "IA - Processar Men<PERSON>m", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Analise a conversa e determine se o paciente precisa de agendamento urgente. Retorne APENAS um JSON:\n\n{\n  \"precisaAgendamento\": true/false,\n  \"urgencia\": 1-4,\n  \"especialidadeSugerida\": \"nome da especialidade\",\n  \"motivo\": \"breve descrição\"\n}\n\nUrgência:\n1 = Rotina (1-2 semanas)\n2 = Moderada (3-7 dias)\n3 = Alta (1-2 dias)\n4 = Emergência (hoje)"}, {"role": "user", "content": "=Mensagem do paciente: {{ $('extrair-dados').item.json.message }}\nResposta da IA: {{ $('ia-resposta').item.json }}"}]}}, "id": "analisar-urgencia", "name": "IA - <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v17.0/{{ $('extrair-dados').item.json.businessPhoneId }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.WHATSAPP_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "body": "={\n  \"messaging_product\": \"whatsapp\",\n  \"to\": \"{{ $('extrair-dados').item.json.phone }}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"{{ $('ia-resposta').item.json }}\"\n  }\n}"}, "id": "enviar-resposta", "name": "Enviar Resposta WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "precisa-agendamento", "leftValue": "={{ $('analisar-urgencia').item.json.precisaAgendamento }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "verificar-agendamento", "name": "Precisa Agendamento?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/agendar-consulta", "sendBody": true, "contentType": "json", "body": "={\n  \"nome\": \"{{ $('buscar-paciente').item.json?.nome || 'Paciente WhatsApp' }}\",\n  \"telefone\": \"{{ $('extrair-dados').item.json.phone }}\",\n  \"pacienteId\": \"{{ $('buscar-paciente').item.json?.id }}\",\n  \"sintomas\": \"{{ $('extrair-dados').item.json.message }}\",\n  \"urgencia\": {{ $('analisar-urgencia').item.json.urgencia }},\n  \"especialidadeSugerida\": \"{{ $('analisar-urgencia').item.json.especialidadeSugerida }}\",\n  \"origem\": \"whatsapp_triagem\"\n}"}, "id": "acionar-agendamento", "name": "Acionar Agendamento Automático", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "conversas_whatsapp", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"telefone": "={{ $('extrair-dados').item.json.phone }}", "mensagem": "={{ $('extrair-dados').item.json.message }}", "resposta_ia": "={{ $('ia-resposta').item.json }}", "urgencia": "={{ $('analisar-urgencia').item.json.urgencia }}", "paciente_id": "={{ $('buscar-paciente').item.json?.id }}", "timestamp": "={{ $now }}"}, "matchingColumns": [], "schema": []}}, "id": "salvar-conversa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2000, 400]}, {"parameters": {"respondWith": "text", "responseBody": "OK"}, "id": "resposta-webhook", "name": "Resposta Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 300]}], "connections": {"Webhook WhatsApp": {"main": [[{"node": "Verificar Mensagem", "type": "main", "index": 0}]]}, "Verificar Mensagem": {"main": [[{"node": "Extrair Dados WhatsApp", "type": "main", "index": 0}]]}, "Extrair Dados WhatsApp": {"main": [[{"node": "Buscar Paciente", "type": "main", "index": 0}]]}, "Buscar Paciente": {"main": [[{"node": "IA - Processar Men<PERSON>m", "type": "main", "index": 0}]]}, "IA - Processar Mensagem": {"main": [[{"node": "IA - <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "IA - Analisar Urgência": {"main": [[{"node": "Enviar Resposta WhatsApp", "type": "main", "index": 0}]]}, "Enviar Resposta WhatsApp": {"main": [[{"node": "Precisa Agendamento?", "type": "main", "index": 0}]]}, "Precisa Agendamento?": {"main": [[{"node": "Acionar Agendamento Automático", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Acionar Agendamento Automático": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Salvar Conversa": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Sao_Paulo"}, "versionId": "1"}