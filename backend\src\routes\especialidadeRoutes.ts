import { Router } from 'express'
import { EspecialidadeController } from '../controllers/especialidadeController'
import { 
  authenticateToken,
  requireAdmin,
  requireMedicoOrRecepcionista,
  logAction,
  rateLimitByUser
} from '../middleware/authMiddleware'

const router = Router()
const especialidadeController = new EspecialidadeController()

/**
 * @route POST /api/especialidades
 * @desc Criar nova especialidade
 * @access Admin
 */
router.post('/',
  authenticateToken,
  requireAdmin,
  rateLimitByUser(20, 60 * 60 * 1000), // 20 criações por hora
  logAction('CREATE_ESPECIALIDADE'),
  especialidadeController.create
)

/**
 * @route GET /api/especialidades
 * @desc Listar especialidades com paginação
 * @access Admin, Recepcionista, Médico
 */
router.get('/',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.findMany
)

/**
 * @route GET /api/especialidades/all
 * @desc Listar todas as especialidades (sem paginação)
 * @access Admin, Recepcionista, Médico
 */
router.get('/all',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.findAll
)

/**
 * @route GET /api/especialidades/with-medicos
 * @desc Buscar especialidades com médicos disponíveis
 * @access Admin, Recepcionista, Médico
 */
router.get('/with-medicos',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.findWithMedicos
)

/**
 * @route GET /api/especialidades/popular
 * @desc Buscar especialidades mais populares
 * @access Admin, Recepcionista
 */
router.get('/popular',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.findMostPopular
)

/**
 * @route GET /api/especialidades/:id
 * @desc Buscar especialidade por ID
 * @access Admin, Recepcionista, Médico
 */
router.get('/:id',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.findById
)

/**
 * @route PUT /api/especialidades/:id
 * @desc Atualizar especialidade
 * @access Admin
 */
router.put('/:id',
  authenticateToken,
  requireAdmin,
  logAction('UPDATE_ESPECIALIDADE'),
  especialidadeController.update
)

/**
 * @route DELETE /api/especialidades/:id
 * @desc Deletar especialidade
 * @access Admin
 */
router.delete('/:id',
  authenticateToken,
  requireAdmin,
  logAction('DELETE_ESPECIALIDADE'),
  especialidadeController.delete
)

/**
 * @route GET /api/especialidades/:id/stats
 * @desc Obter estatísticas da especialidade
 * @access Admin, Recepcionista
 */
router.get('/:id/stats',
  authenticateToken,
  requireMedicoOrRecepcionista,
  especialidadeController.getStats
)

export default router
