-- CreateEnum
CREATE TYPE "public"."TipoUsuario" AS ENUM ('ADMINISTRADOR', 'MEDI<PERSON>', 'RECEPCI<PERSON>IS<PERSON>', 'PACIENTE');

-- CreateEnum
CREATE TYPE "public"."StatusConsulta" AS ENUM ('AGENDADA', 'CONFIRMADA', 'EM_ANDAMENTO', 'CONCLUIDA', 'CANCELAD<PERSON>', 'FALTOU');

-- CreateEnum
CREATE TYPE "public"."StatusUsuario" AS ENUM ('ATIVO', 'INATIVO', 'SUSPENSO');

-- CreateTable
CREATE TABLE "public"."usuarios" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "senha" TEXT NOT NULL,
    "nome" TEXT NOT NULL,
    "telefone" TEXT,
    "tipo" "public"."TipoUsuario" NOT NULL,
    "status" "public"."StatusUsuario" NOT NULL DEFAULT 'ATIVO',
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "usuarios_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."especialidades" (
    "id" TEXT NOT NULL,
    "nome" TEXT NOT NULL,
    "descricao" TEXT,
    "valorConsulta" DECIMAL(10,2) NOT NULL,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "especialidades_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."medicos" (
    "id" TEXT NOT NULL,
    "usuarioId" TEXT NOT NULL,
    "crm" TEXT NOT NULL,
    "biografia" TEXT,
    "foto" TEXT,
    "endereco" TEXT,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "medicos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."medico_especialidades" (
    "id" TEXT NOT NULL,
    "medicoId" TEXT NOT NULL,
    "especialidadeId" TEXT NOT NULL,

    CONSTRAINT "medico_especialidades_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."horarios_atendimento" (
    "id" TEXT NOT NULL,
    "medicoId" TEXT NOT NULL,
    "diaSemana" INTEGER NOT NULL,
    "horaInicio" TEXT NOT NULL,
    "horaFim" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "horarios_atendimento_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pacientes" (
    "id" TEXT NOT NULL,
    "usuarioId" TEXT NOT NULL,
    "cpf" TEXT NOT NULL,
    "dataNascimento" TIMESTAMP(3) NOT NULL,
    "endereco" TEXT,
    "planoSaude" TEXT,
    "contatoEmergencia" TEXT,
    "observacoes" TEXT,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pacientes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."consultas" (
    "id" TEXT NOT NULL,
    "pacienteId" TEXT NOT NULL,
    "medicoId" TEXT NOT NULL,
    "especialidadeId" TEXT NOT NULL,
    "dataHora" TIMESTAMP(3) NOT NULL,
    "status" "public"."StatusConsulta" NOT NULL DEFAULT 'AGENDADA',
    "observacoes" TEXT,
    "valor" DECIMAL(10,2),
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "consultas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."historico_medico" (
    "id" TEXT NOT NULL,
    "pacienteId" TEXT NOT NULL,
    "consultaId" TEXT,
    "titulo" TEXT NOT NULL,
    "descricao" TEXT NOT NULL,
    "prescricao" TEXT,
    "exames" TEXT,
    "anexos" TEXT,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "historico_medico_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."configuracoes" (
    "id" TEXT NOT NULL,
    "chave" TEXT NOT NULL,
    "valor" TEXT NOT NULL,
    "descricao" TEXT,
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "configuracoes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "usuarios_email_key" ON "public"."usuarios"("email");

-- CreateIndex
CREATE UNIQUE INDEX "especialidades_nome_key" ON "public"."especialidades"("nome");

-- CreateIndex
CREATE UNIQUE INDEX "medicos_usuarioId_key" ON "public"."medicos"("usuarioId");

-- CreateIndex
CREATE UNIQUE INDEX "medicos_crm_key" ON "public"."medicos"("crm");

-- CreateIndex
CREATE UNIQUE INDEX "medico_especialidades_medicoId_especialidadeId_key" ON "public"."medico_especialidades"("medicoId", "especialidadeId");

-- CreateIndex
CREATE UNIQUE INDEX "pacientes_usuarioId_key" ON "public"."pacientes"("usuarioId");

-- CreateIndex
CREATE UNIQUE INDEX "pacientes_cpf_key" ON "public"."pacientes"("cpf");

-- CreateIndex
CREATE UNIQUE INDEX "historico_medico_consultaId_key" ON "public"."historico_medico"("consultaId");

-- CreateIndex
CREATE UNIQUE INDEX "configuracoes_chave_key" ON "public"."configuracoes"("chave");

-- AddForeignKey
ALTER TABLE "public"."medicos" ADD CONSTRAINT "medicos_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "public"."usuarios"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."medico_especialidades" ADD CONSTRAINT "medico_especialidades_medicoId_fkey" FOREIGN KEY ("medicoId") REFERENCES "public"."medicos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."medico_especialidades" ADD CONSTRAINT "medico_especialidades_especialidadeId_fkey" FOREIGN KEY ("especialidadeId") REFERENCES "public"."especialidades"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."horarios_atendimento" ADD CONSTRAINT "horarios_atendimento_medicoId_fkey" FOREIGN KEY ("medicoId") REFERENCES "public"."medicos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pacientes" ADD CONSTRAINT "pacientes_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "public"."usuarios"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consultas" ADD CONSTRAINT "consultas_pacienteId_fkey" FOREIGN KEY ("pacienteId") REFERENCES "public"."pacientes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consultas" ADD CONSTRAINT "consultas_medicoId_fkey" FOREIGN KEY ("medicoId") REFERENCES "public"."medicos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."consultas" ADD CONSTRAINT "consultas_especialidadeId_fkey" FOREIGN KEY ("especialidadeId") REFERENCES "public"."especialidades"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."historico_medico" ADD CONSTRAINT "historico_medico_pacienteId_fkey" FOREIGN KEY ("pacienteId") REFERENCES "public"."pacientes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."historico_medico" ADD CONSTRAINT "historico_medico_consultaId_fkey" FOREIGN KEY ("consultaId") REFERENCES "public"."consultas"("id") ON DELETE SET NULL ON UPDATE CASCADE;
