import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import { PrismaClient } from "@prisma/client";

// Importar rotas
import authRoutes from "./routes/authRoutes";
import medicoRoutes from "./routes/medicoRoutes";
import especialidadeRoutes from "./routes/especialidadeRoutes";

// Configurar variáveis de ambiente
dotenv.config();

// Inicializar Prisma
const prisma = new PrismaClient();

// Criar aplicação Express
const app = express();

// Configurações de segurança
app.use(helmet());

// Configurar CORS
app.use(
  cors({
    origin: process.env.APP_URL || "http://localhost:3000",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Middleware para parsing de JSON
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Logging de requisições
app.use(morgan("combined"));

// Middleware para adicionar timestamp nas requisições
app.use((req: any, res, next) => {
  req.timestamp = new Date().toISOString();
  next();
});

// Rota de health check
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    version: "1.0.0",
  });
});

// Rotas da API
app.use("/api/auth", authRoutes);
app.use("/api/medicos", medicoRoutes);
app.use("/api/especialidades", especialidadeRoutes);

// Rota para informações da API
app.get("/api", (req, res) => {
  res.json({
    name: "Agenteia Médicos API",
    version: "1.0.0",
    description: "API para sistema de agência médica",
    endpoints: {
      auth: "/api/auth",
      medicos: "/api/medicos",
      especialidades: "/api/especialidades",
      health: "/api/health",
    },
  });
});

// Middleware para rotas não encontradas
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Rota não encontrada",
    path: req.originalUrl,
  });
});

// Middleware global de tratamento de erros
app.use(
  (
    error: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Erro não tratado:", error);

    res.status(500).json({
      success: false,
      message: "Erro interno do servidor",
      ...(process.env.NODE_ENV === "development" && {
        error: error.message,
        stack: error.stack,
      }),
    });
  }
);

// Configurar porta
const PORT = process.env.PORT || 3001;

// Função para inicializar o servidor
async function startServer() {
  try {
    // Testar conexão com banco de dados
    await prisma.$connect();
    console.log("✅ Conectado ao banco de dados");

    // Iniciar servidor
    app.listen(PORT, () => {
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
      console.log(`📍 API docs: http://localhost:${PORT}/api`);
      console.log(`🌍 Ambiente: ${process.env.NODE_ENV || "development"}`);
    });
  } catch (error) {
    console.error("❌ Erro ao inicializar servidor:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Recebido SIGINT. Encerrando servidor...");

  try {
    await prisma.$disconnect();
    console.log("✅ Desconectado do banco de dados");
    process.exit(0);
  } catch (error) {
    console.error("❌ Erro ao desconectar do banco:", error);
    process.exit(1);
  }
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Recebido SIGTERM. Encerrando servidor...");

  try {
    await prisma.$disconnect();
    console.log("✅ Desconectado do banco de dados");
    process.exit(0);
  } catch (error) {
    console.error("❌ Erro ao desconectar do banco:", error);
    process.exit(1);
  }
});

// Inicializar servidor
startServer();

export default app;
