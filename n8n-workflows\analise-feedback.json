{"name": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "nodes": [{"parameters": {"httpMethod": "POST", "path": "feedback", "responseMode": "responseNode", "options": {}}, "id": "webhook-feedback", "name": "Webhook Feedback", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Você é um especialista em análise de sentimentos para feedback médico. Analise o feedback do paciente e retorne APENAS um JSON com:\n\n{\n  \"sentimento\": \"positivo|neutro|negativo\",\n  \"score\": 1-10,\n  \"categoria\": \"atendimento|medico|infraestrutura|agendamento|preco|outros\",\n  \"urgencia\": \"baixa|media|alta\",\n  \"resumo\": \"breve resumo do feedback\",\n  \"acoes_sugeridas\": [\"ação1\", \"ação2\"],\n  \"palavras_chave\": [\"palavra1\", \"palavra2\"]\n}\n\nCritérios:\n- Score: 1-3 (negativo), 4-6 (neutro), 7-10 (positivo)\n- Urgência alta: reclamaç<PERSON><PERSON> graves, problemas de segurança\n- Urgência média: insatisfação moderada, sugestões importantes\n- Urgência baixa: elogios, sugestões menores"}, {"role": "user", "content": "=Feedback do paciente:\nNome: {{ $json.paciente_nome }}\nMédico: {{ $json.medico_nome }}\nEspecialidade: {{ $json.especialidade }}\nData: {{ $json.data_consulta }}\nNota: {{ $json.nota }}/5\nComentário: {{ $json.comentario }}\nRecomendaria: {{ $json.recomendaria ? 'Sim' : 'Não' }}"}]}}, "id": "analisar-sentimento", "name": "IA - <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "feedback_analises", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"consulta_id": "={{ $('webhook-feedback').item.json.consulta_id }}", "paciente_nome": "={{ $('webhook-feedback').item.json.paciente_nome }}", "medico_nome": "={{ $('webhook-feedback').item.json.medico_nome }}", "especialidade": "={{ $('webhook-feedback').item.json.especialidade }}", "nota": "={{ $('webhook-feedback').item.json.nota }}", "comentario": "={{ $('webhook-feedback').item.json.comentario }}", "recomendaria": "={{ $('webhook-feedback').item.json.recomendaria }}", "sentimento": "={{ $('analisar-sentimento').item.json.sentimento }}", "score": "={{ $('analisar-sentimento').item.json.score }}", "categoria": "={{ $('analisar-sentimento').item.json.categoria }}", "urgencia": "={{ $('analisar-sentimento').item.json.urgencia }}", "resumo": "={{ $('analisar-sentimento').item.json.resumo }}", "acoes_sugeridas": "={{ JSON.stringify($('analisar-sentimento').item.json.acoes_sugeridas) }}", "palavras_chave": "={{ JSON.stringify($('analisar-sentimento').item.json.palavras_chave) }}", "analisado_em": "={{ $now }}"}, "matchingColumns": [], "schema": []}}, "id": "salvar-analise", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "urgencia-alta", "leftValue": "={{ $('analisar-sentimento').item.json.urgencia }}", "rightValue": "alta", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "verificar-urgencia", "name": "Urgência Alta?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Crie um alerta urgente para a gestão sobre feedback negativo. O alerta deve:\n\n1. Ser CLARO e OBJETIVO\n2. Destacar a GRAVIDADE\n3. Sugerir AÇÕES IMEDIATAS\n4. Incluir dados relevantes\n\nFormato: Assunto + Corpo da mensagem para Slack/Teams"}, {"role": "user", "content": "=FEEDBACK URGENTE:\nPaciente: {{ $('webhook-feedback').item.json.paciente_nome }}\nMédico: {{ $('webhook-feedback').item.json.medico_nome }}\nNota: {{ $('webhook-feedback').item.json.nota }}/5\nComentário: {{ $('webhook-feedback').item.json.comentario }}\nAnálise IA: {{ $('analisar-sentimento').item.json.resumo }}\nAções sugeridas: {{ $('analisar-sentimento').item.json.acoes_sugeridas.join(', ') }}"}]}}, "id": "gerar-<PERSON>a", "name": "IA - <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"method": "POST", "url": "{{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "contentType": "json", "body": "={\n  \"text\": \"🚨 FEEDBACK URGENTE - Agenteia Médicos\",\n  \"attachments\": [\n    {\n      \"color\": \"danger\",\n      \"title\": \"Feedback Negativo Detectado\",\n      \"text\": \"{{ $('gerar-alerta').item.json }}\",\n      \"fields\": [\n        {\n          \"title\": \"Paciente\",\n          \"value\": \"{{ $('webhook-feedback').item.json.paciente_nome }}\",\n          \"short\": true\n        },\n        {\n          \"title\": \"Médico\",\n          \"value\": \"{{ $('webhook-feedback').item.json.medico_nome }}\",\n          \"short\": true\n        },\n        {\n          \"title\": \"Nota\",\n          \"value\": \"{{ $('webhook-feedback').item.json.nota }}/5\",\n          \"short\": true\n        },\n        {\n          \"title\": \"Sentimento\",\n          \"value\": \"{{ $('analisar-sentimento').item.json.sentimento }} ({{ $('analisar-sentimento').item.json.score }}/10)\",\n          \"short\": true\n        }\n      ],\n      \"footer\": \"Sistema de Análise IA\",\n      \"ts\": {{ Math.floor(Date.now() / 1000) }}\n    }\n  ]\n}"}, "id": "enviar-slack", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "🚨 URGENTE: Feedback Negativo - {{ $('webhook-feedback').item.json.paciente_nome }}", "emailType": "html", "message": "=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title><PERSON><PERSON><PERSON> de Feedback</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n        <div style=\"background: #dc3545; color: white; padding: 20px; border-radius: 10px 10px 0 0;\">\n            <h1 style=\"margin: 0; font-size: 24px;\">🚨 ALERTA URGENTE</h1>\n            <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Feedback Negativo Detectado</p>\n        </div>\n        \n        <div style=\"background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">\n            <div style=\"background: #fff; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 20px 0;\">\n                <h3 style=\"margin-top: 0; color: #dc3545;\">📋 Detalhes do Feedback</h3>\n                <p><strong>👤 Paciente:</strong> {{ $('webhook-feedback').item.json.paciente_nome }}</p>\n                <p><strong>👨‍⚕️ Médico:</strong> {{ $('webhook-feedback').item.json.medico_nome }}</p>\n                <p><strong>🏥 Especialidade:</strong> {{ $('webhook-feedback').item.json.especialidade }}</p>\n                <p><strong>⭐ Nota:</strong> {{ $('webhook-feedback').item.json.nota }}/5</p>\n                <p><strong>📝 Comentário:</strong></p>\n                <blockquote style=\"background: #f1f3f4; padding: 15px; border-left: 3px solid #ccc; margin: 10px 0; font-style: italic;\">\n                    {{ $('webhook-feedback').item.json.comentario }}\n                </blockquote>\n            </div>\n            \n            <div style=\"background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;\">\n                <h3 style=\"margin-top: 0; color: #856404;\">🤖 Análise da IA</h3>\n                <p><strong>Sentimento:</strong> {{ $('analisar-sentimento').item.json.sentimento }} ({{ $('analisar-sentimento').item.json.score }}/10)</p>\n                <p><strong>Categoria:</strong> {{ $('analisar-sentimento').item.json.categoria }}</p>\n                <p><strong>Resumo:</strong> {{ $('analisar-sentimento').item.json.resumo }}</p>\n                <p><strong>Palavras-chave:</strong> {{ $('analisar-sentimento').item.json.palavras_chave.join(', ') }}</p>\n            </div>\n            \n            <div style=\"background: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8; margin: 20px 0;\">\n                <h3 style=\"margin-top: 0; color: #0c5460;\">💡 Ações Sugeridas</h3>\n                <ul>\n                    {{ $('analisar-sentimento').item.json.acoes_sugeridas.map(acao => `<li>${acao}</li>`).join('') }}\n                </ul>\n            </div>\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <p style=\"margin: 0; color: #dc3545; font-weight: bold;\">⚠️ AÇÃO IMEDIATA NECESSÁRIA</p>\n                <p style=\"margin: 5px 0; color: #6c757d;\">Este feedback requer atenção urgente da gestão</p>\n            </div>\n        </div>\n        \n        <div style=\"text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;\">\n            <p>Alerta gerado automaticamente pelo sistema de IA</p>\n            <p>{{ $now.format('DD/MM/YYYY HH:mm:ss') }}</p>\n        </div>\n    </div>\n</body>\n</html>"}, "id": "enviar-email-alerta", "name": "Envia<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "feedback-positivo", "leftValue": "={{ $('analisar-sentimento').item.json.sentimento }}", "rightValue": "positivo", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "verificar-positivo", "name": "Feedback Positivo?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Crie uma mensagem de agradecimento personalizada para o paciente que deu feedback positivo. A mensagem deve:\n\n1. Ser CALOROSA e GENUÍNA\n2. Mencionar pontos específicos do feedback\n3. Reforçar o compromisso com a qualidade\n4. Convidar para futuras consultas\n\nTom: Profissional mas acolhedor, máximo 200 caracteres para WhatsApp"}, {"role": "user", "content": "=Paciente: {{ $('webhook-feedback').item.json.paciente_nome }}\nMédico: {{ $('webhook-feedback').item.json.medico_nome }}\nNota: {{ $('webhook-feedback').item.json.nota }}/5\nComentário: {{ $('webhook-feedback').item.json.comentario }}\nPontos positivos: {{ $('analisar-sentimento').item.json.palavras_chave.join(', ') }}"}]}}, "id": "gerar-agradecimento", "name": "IA - Gerar <PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 600]}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v17.0/{{ $vars.WHATSAPP_PHONE_ID }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.WHATSAPP_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "body": "={\n  \"messaging_product\": \"whatsapp\",\n  \"to\": \"{{ $('webhook-feedback').item.json.telefone.replace(/\\D/g, '') }}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"{{ $('gerar-agradecimento').item.json }}\"\n  }\n}"}, "id": "enviar-agradecimento", "name": "Enviar Agradecimento WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 600]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Feedback analisado com sucesso\",\n  \"analise\": {\n    \"sentimento\": \"{{ $('analisar-sentimento').item.json.sentimento }}\",\n    \"score\": {{ $('analisar-sentimento').item.json.score }},\n    \"categoria\": \"{{ $('analisar-sentimento').item.json.categoria }}\",\n    \"urgencia\": \"{{ $('analisar-sentimento').item.json.urgencia }}\"\n  }\n}"}, "id": "resposta-webhook", "name": "Resposta Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 400]}], "connections": {"Webhook Feedback": {"main": [[{"node": "IA - <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "IA - Analisar Sentimento": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Salvar Análise": {"main": [[{"node": "Urgência Alta?", "type": "main", "index": 0}, {"node": "Feedback Positivo?", "type": "main", "index": 0}]]}, "Urgência Alta?": {"main": [[{"node": "IA - <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "IA - Gerar Alerta Urgente": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Envia<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Enviar Alerta Slack": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}, "Enviar Email Alerta": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}, "Feedback Positivo?": {"main": [[{"node": "IA - Gerar <PERSON>", "type": "main", "index": 0}]]}, "IA - Gerar Agradecimento": {"main": [[{"node": "Enviar Agradecimento WhatsApp", "type": "main", "index": 0}]]}, "Enviar Agradecimento WhatsApp": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Sao_Paulo"}, "versionId": "1"}