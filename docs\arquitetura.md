# Arquitetura do Sistema - Agenteia Médicos

## Visão Geral

O sistema é construído seguindo uma arquitetura de microserviços com separação clara entre frontend e backend, utilizando tecnologias modernas e práticas de desenvolvimento.

## Arquitetura Geral

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Banco de      │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│   Dados         │
│                 │    │                 │    │   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Supabase      │    │   Serviços      │    │   Storage       │
│   Auth          │    │   Externos      │    │   (Supabase)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Camadas da Aplicação

### 1. Camada de Apresentação (Frontend)
- **Framework**: Next.js 14 com App Router
- **Linguagem**: TypeScript
- **Estilização**: Tailwind CSS + Shadcn/ui
- **Estado**: Zustand para gerenciamento de estado global
- **Formulários**: React Hook Form + Zod para validação

#### Estrutura do Frontend
```
frontend/
├── src/
│   ├── app/                 # App Router (Next.js 14)
│   │   ├── (auth)/         # Grupo de rotas de autenticação
│   │   ├── (dashboard)/    # Grupo de rotas do dashboard
│   │   └── api/            # API Routes
│   ├── components/         # Componentes reutilizáveis
│   │   ├── ui/            # Componentes base (shadcn/ui)
│   │   ├── forms/         # Componentes de formulário
│   │   └── layout/        # Componentes de layout
│   ├── lib/               # Utilitários e configurações
│   ├── hooks/             # Custom hooks
│   ├── stores/            # Stores do Zustand
│   └── types/             # Tipos TypeScript
```

### 2. Camada de Negócio (Backend)
- **Framework**: Express.js
- **Linguagem**: TypeScript
- **ORM**: Prisma
- **Autenticação**: JWT + Supabase Auth
- **Validação**: Zod

#### Estrutura do Backend
```
backend/
├── src/
│   ├── controllers/       # Controladores das rotas
│   ├── services/          # Lógica de negócio
│   ├── repositories/      # Acesso aos dados
│   ├── middleware/        # Middlewares
│   ├── routes/            # Definição das rotas
│   ├── utils/             # Utilitários
│   └── types/             # Tipos TypeScript
├── prisma/
│   ├── schema.prisma      # Schema do banco
│   └── migrations/        # Migrações
```

### 3. Camada de Dados
- **Banco Principal**: PostgreSQL
- **ORM**: Prisma
- **Autenticação**: Supabase Auth
- **Storage**: Supabase Storage para arquivos

## Padrões de Arquitetura

### 1. Repository Pattern
Separação entre lógica de negócio e acesso aos dados:
```typescript
// Repository
class MedicoRepository {
  async findById(id: string): Promise<Medico | null>
  async create(data: CreateMedicoData): Promise<Medico>
  async update(id: string, data: UpdateMedicoData): Promise<Medico>
}

// Service
class MedicoService {
  constructor(private medicoRepository: MedicoRepository) {}
  
  async criarMedico(data: CreateMedicoData): Promise<Medico> {
    // Lógica de negócio
    return this.medicoRepository.create(data)
  }
}
```

### 2. DTO (Data Transfer Objects)
Objetos para transferência de dados entre camadas:
```typescript
export interface CreateMedicoDTO {
  nome: string
  crm: string
  especialidades: string[]
  email: string
  telefone: string
}
```

### 3. Middleware Pattern
Interceptadores para autenticação, validação e logging:
```typescript
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Verificar token JWT
}

export const validateSchema = (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => {
  // Validar dados com Zod
}
```

## Segurança

### 1. Autenticação
- JWT tokens para sessões
- Refresh tokens para renovação
- Supabase Auth para gerenciamento de usuários

### 2. Autorização
- RBAC (Role-Based Access Control)
- Middleware de autorização por rota
- Validação de permissões no frontend e backend

### 3. Validação de Dados
- Validação no frontend com Zod
- Validação no backend com Zod
- Sanitização de dados de entrada

## Performance

### 1. Frontend
- Server-Side Rendering (SSR) com Next.js
- Static Site Generation (SSG) para páginas estáticas
- Code splitting automático
- Otimização de imagens com Next.js Image

### 2. Backend
- Connection pooling do Prisma
- Cache com Redis (futuro)
- Paginação em consultas
- Índices otimizados no banco

### 3. Banco de Dados
- Índices em campos de busca frequente
- Relacionamentos otimizados
- Queries otimizadas com Prisma

## Monitoramento e Logs

### 1. Logs
- Winston para logging estruturado
- Diferentes níveis de log (error, warn, info, debug)
- Logs centralizados

### 2. Métricas
- Tempo de resposta das APIs
- Uso de recursos
- Erros e exceções

## Deployment

### 1. Desenvolvimento
- Docker para ambiente local
- Hot reload no frontend e backend
- Banco local com Docker Compose

### 2. Produção
- Vercel para frontend (Next.js)
- Railway/Heroku para backend
- Supabase para banco e auth
- CI/CD com GitHub Actions

## Escalabilidade

### 1. Horizontal
- Múltiplas instâncias do backend
- Load balancer
- CDN para assets estáticos

### 2. Vertical
- Otimização de queries
- Cache de dados frequentes
- Compressão de responses

## Tecnologias Utilizadas

### Frontend
- Next.js 14
- TypeScript
- Tailwind CSS
- Shadcn/ui
- React Hook Form
- Zod
- Zustand

### Backend
- Node.js
- Express.js
- TypeScript
- Prisma
- Zod
- Winston
- Jest

### Banco de Dados
- PostgreSQL
- Supabase

### DevOps
- Docker
- GitHub Actions
- Vercel
- Railway/Heroku
