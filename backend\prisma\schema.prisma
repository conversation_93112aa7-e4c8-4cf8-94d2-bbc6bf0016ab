// Schema do banco de dados para Sistema de Agência Médica
// Gerado com Prisma ORM

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum para tipos de usuário
enum TipoUsuario {
  ADMINISTRADOR
  MEDICO
  RECEPCIONISTA
  PACIENTE
}

// Enum para status de consulta
enum StatusConsulta {
  AGENDADA
  CONFIRMADA
  EM_ANDAMENTO
  CONCLUIDA
  CANCELADA
  FALTOU
}

// Enum para status de usuário
enum StatusUsuario {
  ATIVO
  INATIVO
  SUSPENSO
}

// Modelo de Usuário (base para todos os tipos)
model Usuario {
  id        String   @id @default(cuid())
  email     String   @unique
  senha     String
  nome      String
  telefone  String?
  tipo      TipoUsuario
  status    StatusUsuario @default(ATIVO)
  criadoEm  DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  medico       Medico?
  paciente     Paciente?

  @@map("usuarios")
}

// Modelo de Especialidade Médica
model Especialidade {
  id          String   @id @default(cuid())
  nome        String   @unique
  descricao   String?
  valorConsulta Decimal @db.Decimal(10, 2)
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  medicos     MedicoEspecialidade[]
  consultas   Consulta[]

  @@map("especialidades")
}

// Modelo de Médico
model Medico {
  id          String   @id @default(cuid())
  usuarioId   String   @unique
  crm         String   @unique
  biografia   String?
  foto        String?
  endereco    String?
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  usuario       Usuario @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
  especialidades MedicoEspecialidade[]
  horarios      HorarioAtendimento[]
  consultas     Consulta[]

  @@map("medicos")
}

// Tabela de relacionamento Médico-Especialidade (muitos para muitos)
model MedicoEspecialidade {
  id             String @id @default(cuid())
  medicoId       String
  especialidadeId String

  // Relacionamentos
  medico        Medico @relation(fields: [medicoId], references: [id], onDelete: Cascade)
  especialidade Especialidade @relation(fields: [especialidadeId], references: [id], onDelete: Cascade)

  @@unique([medicoId, especialidadeId])
  @@map("medico_especialidades")
}

// Modelo de Horário de Atendimento
model HorarioAtendimento {
  id          String   @id @default(cuid())
  medicoId    String
  diaSemana   Int      // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
  horaInicio  String   // Formato HH:MM
  horaFim     String   // Formato HH:MM
  ativo       Boolean  @default(true)
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  medico      Medico @relation(fields: [medicoId], references: [id], onDelete: Cascade)

  @@map("horarios_atendimento")
}

// Modelo de Paciente
model Paciente {
  id          String   @id @default(cuid())
  usuarioId   String   @unique
  cpf         String   @unique
  dataNascimento DateTime
  endereco    String?
  planoSaude  String?
  contatoEmergencia String?
  observacoes String?
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  usuario     Usuario @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
  consultas   Consulta[]
  historico   HistoricoMedico[]

  @@map("pacientes")
}

// Modelo de Consulta
model Consulta {
  id             String   @id @default(cuid())
  pacienteId     String
  medicoId       String
  especialidadeId String
  dataHora       DateTime
  status         StatusConsulta @default(AGENDADA)
  observacoes    String?
  valor          Decimal? @db.Decimal(10, 2)
  criadoEm       DateTime @default(now())
  atualizadoEm   DateTime @updatedAt

  // Relacionamentos
  paciente      Paciente @relation(fields: [pacienteId], references: [id], onDelete: Cascade)
  medico        Medico @relation(fields: [medicoId], references: [id], onDelete: Cascade)
  especialidade Especialidade @relation(fields: [especialidadeId], references: [id])
  historico     HistoricoMedico?

  @@map("consultas")
}

// Modelo de Histórico Médico
model HistoricoMedico {
  id          String   @id @default(cuid())
  pacienteId  String
  consultaId  String?  @unique
  titulo      String
  descricao   String
  prescricao  String?
  exames      String?  // JSON com lista de exames
  anexos      String?  // JSON com lista de anexos
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  // Relacionamentos
  paciente    Paciente @relation(fields: [pacienteId], references: [id], onDelete: Cascade)
  consulta    Consulta? @relation(fields: [consultaId], references: [id])

  @@map("historico_medico")
}

// Modelo de Configurações do Sistema
model Configuracao {
  id          String   @id @default(cuid())
  chave       String   @unique
  valor       String
  descricao   String?
  criadoEm    DateTime @default(now())
  atualizadoEm DateTime @updatedAt

  @@map("configuracoes")
}
