import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { TipoUsuario, StatusUsuario } from '../../../shared/src/types'

const prisma = new PrismaClient()

export interface TokenPayload {
  userId: string
  email: string
  tipo: TipoUsuario
}

export interface LoginCredentials {
  email: string
  senha: string
}

export interface RegisterData {
  email: string
  senha: string
  nome: string
  telefone?: string
  tipo: TipoUsuario
}

export class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'
  private readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'
  private readonly SALT_ROUNDS = 12

  /**
   * Gera hash da senha
   */
  async hashPassword(senha: string): Promise<string> {
    return bcrypt.hash(senha, this.SALT_ROUNDS)
  }

  /**
   * Verifica se a senha está correta
   */
  async verifyPassword(senha: string, hash: string): Promise<boolean> {
    return bcrypt.compare(senha, hash)
  }

  /**
   * Gera token JWT
   */
  generateToken(payload: TokenPayload): string {
    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
      issuer: 'agenteia-medicos',
      audience: 'agenteia-medicos-users'
    })
  }

  /**
   * Verifica e decodifica token JWT
   */
  verifyToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, this.JWT_SECRET, {
        issuer: 'agenteia-medicos',
        audience: 'agenteia-medicos-users'
      }) as TokenPayload
    } catch (error) {
      throw new Error('Token inválido')
    }
  }

  /**
   * Realiza login do usuário
   */
  async login(credentials: LoginCredentials) {
    const { email, senha } = credentials

    // Buscar usuário por email
    const usuario = await prisma.usuario.findUnique({
      where: { email },
      include: {
        medico: {
          include: {
            especialidades: {
              include: {
                especialidade: true
              }
            }
          }
        },
        paciente: true
      }
    })

    if (!usuario) {
      throw new Error('Credenciais inválidas')
    }

    // Verificar se usuário está ativo
    if (usuario.status !== StatusUsuario.ATIVO) {
      throw new Error('Usuário inativo ou suspenso')
    }

    // Verificar senha
    const senhaValida = await this.verifyPassword(senha, usuario.senha)
    if (!senhaValida) {
      throw new Error('Credenciais inválidas')
    }

    // Gerar token
    const tokenPayload: TokenPayload = {
      userId: usuario.id,
      email: usuario.email,
      tipo: usuario.tipo as TipoUsuario
    }

    const token = this.generateToken(tokenPayload)

    // Remover senha do retorno
    const { senha: _, ...usuarioSemSenha } = usuario

    return {
      token,
      usuario: usuarioSemSenha
    }
  }

  /**
   * Registra novo usuário
   */
  async register(data: RegisterData) {
    const { email, senha, nome, telefone, tipo } = data

    // Verificar se email já existe
    const usuarioExistente = await prisma.usuario.findUnique({
      where: { email }
    })

    if (usuarioExistente) {
      throw new Error('Email já está em uso')
    }

    // Hash da senha
    const senhaHash = await this.hashPassword(senha)

    // Criar usuário
    const usuario = await prisma.usuario.create({
      data: {
        email,
        senha: senhaHash,
        nome,
        telefone,
        tipo,
        status: StatusUsuario.ATIVO
      }
    })

    // Gerar token
    const tokenPayload: TokenPayload = {
      userId: usuario.id,
      email: usuario.email,
      tipo: usuario.tipo as TipoUsuario
    }

    const token = this.generateToken(tokenPayload)

    // Remover senha do retorno
    const { senha: _, ...usuarioSemSenha } = usuario

    return {
      token,
      usuario: usuarioSemSenha
    }
  }

  /**
   * Busca usuário por ID
   */
  async getUserById(userId: string) {
    const usuario = await prisma.usuario.findUnique({
      where: { id: userId },
      include: {
        medico: {
          include: {
            especialidades: {
              include: {
                especialidade: true
              }
            }
          }
        },
        paciente: true
      }
    })

    if (!usuario) {
      throw new Error('Usuário não encontrado')
    }

    // Remover senha do retorno
    const { senha: _, ...usuarioSemSenha } = usuario

    return usuarioSemSenha
  }

  /**
   * Atualiza senha do usuário
   */
  async updatePassword(userId: string, senhaAtual: string, novaSenha: string) {
    const usuario = await prisma.usuario.findUnique({
      where: { id: userId }
    })

    if (!usuario) {
      throw new Error('Usuário não encontrado')
    }

    // Verificar senha atual
    const senhaValida = await this.verifyPassword(senhaAtual, usuario.senha)
    if (!senhaValida) {
      throw new Error('Senha atual incorreta')
    }

    // Hash da nova senha
    const novaSenhaHash = await this.hashPassword(novaSenha)

    // Atualizar senha
    await prisma.usuario.update({
      where: { id: userId },
      data: { senha: novaSenhaHash }
    })

    return { message: 'Senha atualizada com sucesso' }
  }

  /**
   * Solicita reset de senha (gera token temporário)
   */
  async requestPasswordReset(email: string) {
    const usuario = await prisma.usuario.findUnique({
      where: { email }
    })

    if (!usuario) {
      // Por segurança, não revelar se o email existe
      return { message: 'Se o email existir, um link de reset será enviado' }
    }

    // Gerar token temporário (válido por 1 hora)
    const resetToken = jwt.sign(
      { userId: usuario.id, type: 'password-reset' },
      this.JWT_SECRET,
      { expiresIn: '1h' }
    )

    // TODO: Enviar email com link de reset
    // Por enquanto, apenas retornar o token (em produção, enviar por email)
    
    return {
      message: 'Link de reset enviado por email',
      resetToken // Remover em produção
    }
  }

  /**
   * Reset de senha com token
   */
  async resetPassword(token: string, novaSenha: string) {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any

      if (decoded.type !== 'password-reset') {
        throw new Error('Token inválido')
      }

      // Hash da nova senha
      const novaSenhaHash = await this.hashPassword(novaSenha)

      // Atualizar senha
      await prisma.usuario.update({
        where: { id: decoded.userId },
        data: { senha: novaSenhaHash }
      })

      return { message: 'Senha resetada com sucesso' }
    } catch (error) {
      throw new Error('Token inválido ou expirado')
    }
  }

  /**
   * Valida permissões do usuário
   */
  hasPermission(userType: TipoUsuario, requiredTypes: TipoUsuario[]): boolean {
    return requiredTypes.includes(userType)
  }

  /**
   * Verifica se usuário é admin
   */
  isAdmin(userType: TipoUsuario): boolean {
    return userType === TipoUsuario.ADMINISTRADOR
  }

  /**
   * Verifica se usuário pode acessar recurso
   */
  canAccessResource(userType: TipoUsuario, resourceOwnerId?: string, userId?: string): boolean {
    // Admin pode acessar tudo
    if (this.isAdmin(userType)) {
      return true
    }

    // Se não há dono do recurso, permitir
    if (!resourceOwnerId) {
      return true
    }

    // Usuário pode acessar seus próprios recursos
    return resourceOwnerId === userId
  }
}
