import { Router } from 'express'
import { AuthController } from '../controllers/authController'
import { 
  authenticateToken, 
  rateLimitByUser,
  logAction 
} from '../middleware/authMiddleware'

const router = Router()
const authController = new AuthController()

// Rate limiting para rotas de autenticação (mais restritivo)
const authRateLimit = rateLimitByUser(5, 15 * 60 * 1000) // 5 tentativas por 15 minutos

/**
 * @route POST /api/auth/login
 * @desc Login de usuário
 * @access Public
 */
router.post('/login', 
  authRateLimit,
  logAction('LOGIN_ATTEMPT'),
  authController.login
)

/**
 * @route POST /api/auth/register
 * @desc Registro de novo usuário
 * @access Public
 */
router.post('/register',
  authRateLimit,
  logAction('REGISTER_ATTEMPT'),
  authController.register
)

/**
 * @route GET /api/auth/me
 * @desc Obter dados do usuário atual
 * @access Private
 */
router.get('/me',
  authenticateToken,
  authController.me
)

/**
 * @route PUT /api/auth/password
 * @desc Atualizar senha do usuário
 * @access Private
 */
router.put('/password',
  authenticateToken,
  rateLimitByUser(3, 60 * 60 * 1000), // 3 tentativas por hora
  logAction('PASSWORD_UPDATE'),
  authController.updatePassword
)

/**
 * @route POST /api/auth/forgot-password
 * @desc Solicitar reset de senha
 * @access Public
 */
router.post('/forgot-password',
  rateLimitByUser(3, 60 * 60 * 1000), // 3 tentativas por hora
  logAction('PASSWORD_RESET_REQUEST'),
  authController.requestPasswordReset
)

/**
 * @route POST /api/auth/reset-password
 * @desc Reset de senha com token
 * @access Public
 */
router.post('/reset-password',
  rateLimitByUser(3, 60 * 60 * 1000), // 3 tentativas por hora
  logAction('PASSWORD_RESET'),
  authController.resetPassword
)

/**
 * @route POST /api/auth/logout
 * @desc Logout de usuário
 * @access Private
 */
router.post('/logout',
  authenticateToken,
  logAction('LOGOUT'),
  authController.logout
)

/**
 * @route POST /api/auth/verify-token
 * @desc Verificar se token é válido
 * @access Public
 */
router.post('/verify-token',
  authController.verifyToken
)

/**
 * @route POST /api/auth/refresh-token
 * @desc Renovar token de acesso
 * @access Private
 */
router.post('/refresh-token',
  authenticateToken,
  authController.refreshToken
)

export default router
