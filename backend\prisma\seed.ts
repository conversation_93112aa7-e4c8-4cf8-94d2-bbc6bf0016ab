import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...')

  // Criar especialidades médicas
  console.log('📋 Criando especialidades médicas...')
  
  const especialidades = [
    { nome: 'Cardiologia', descricao: 'Especialidade médica que se ocupa do diagnóstico e tratamento das doenças que acometem o coração', valorConsulta: 250.00 },
    { nome: 'Dermatologia', descricao: 'Especialidade médica que se ocupa do diagnóstico, tratamento e prevenção de doenças da pele', valorConsulta: 200.00 },
    { nome: 'Endocrinologia', descricao: 'Especialidade médica que cuida dos transtornos das glândulas endócrinas', valorConsulta: 280.00 },
    { nome: 'Ginecologia', descricao: 'Especialidade médica que trata de doenças do sistema reprodutor feminino', valorConsulta: 220.00 },
    { nome: 'Neurologia', descricao: 'Especialidade médica que trata dos distúrbios estruturais do sistema nervoso', valorConsulta: 300.00 },
    { nome: 'Oftalmologia', descricao: 'Especialidade médica que investiga e trata as doenças relacionadas aos olhos', valorConsulta: 180.00 },
    { nome: 'Ortopedia', descricao: 'Especialidade médica que cuida da saúde relacionada aos elementos do aparelho locomotor', valorConsulta: 260.00 },
    { nome: 'Pediatria', descricao: 'Especialidade médica dedicada à assistência à criança e ao adolescente', valorConsulta: 200.00 },
    { nome: 'Psiquiatria', descricao: 'Especialidade médica que lida com a prevenção, atendimento, diagnóstico, tratamento e reabilitação das diferentes formas de sofrimentos mentais', valorConsulta: 250.00 },
    { nome: 'Urologia', descricao: 'Especialidade médica que trata do trato urinário de homens e mulheres e do sistema reprodutor masculino', valorConsulta: 240.00 }
  ]

  for (const esp of especialidades) {
    await prisma.especialidade.upsert({
      where: { nome: esp.nome },
      update: {},
      create: esp
    })
  }

  console.log('✅ Especialidades criadas com sucesso!')

  // Criar usuário administrador
  console.log('👤 Criando usuário administrador...')
  
  const senhaAdmin = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.usuario.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      senha: senhaAdmin,
      nome: 'Administrador do Sistema',
      telefone: '(11) 99999-9999',
      tipo: 'ADMINISTRADOR',
      status: 'ATIVO'
    }
  })

  console.log('✅ Usuário administrador criado!')
  console.log('📧 Email: <EMAIL>')
  console.log('🔑 Senha: admin123')

  // Criar usuário recepcionista
  console.log('👥 Criando usuário recepcionista...')
  
  const senhaRecepcionista = await bcrypt.hash('recepcao123', 12)
  
  const recepcionista = await prisma.usuario.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      senha: senhaRecepcionista,
      nome: 'Maria da Recepção',
      telefone: '(11) 98888-8888',
      tipo: 'RECEPCIONISTA',
      status: 'ATIVO'
    }
  })

  console.log('✅ Usuário recepcionista criado!')
  console.log('📧 Email: <EMAIL>')
  console.log('🔑 Senha: recepcao123')

  // Criar médico de exemplo
  console.log('👨‍⚕️ Criando médico de exemplo...')
  
  const senhaMedico = await bcrypt.hash('medico123', 12)
  
  const usuarioMedico = await prisma.usuario.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      senha: senhaMedico,
      nome: 'Dr. João Silva',
      telefone: '(11) 97777-7777',
      tipo: 'MEDICO',
      status: 'ATIVO'
    }
  })

  const cardiologia = await prisma.especialidade.findUnique({
    where: { nome: 'Cardiologia' }
  })

  if (cardiologia) {
    const medico = await prisma.medico.upsert({
      where: { usuarioId: usuarioMedico.id },
      update: {},
      create: {
        usuarioId: usuarioMedico.id,
        crm: '123456/SP',
        biografia: 'Cardiologista com mais de 15 anos de experiência em diagnóstico e tratamento de doenças cardiovasculares.',
        endereco: 'Rua das Flores, 123 - São Paulo/SP'
      }
    })

    // Associar especialidade ao médico
    await prisma.medicoEspecialidade.upsert({
      where: {
        medicoId_especialidadeId: {
          medicoId: medico.id,
          especialidadeId: cardiologia.id
        }
      },
      update: {},
      create: {
        medicoId: medico.id,
        especialidadeId: cardiologia.id
      }
    })

    // Criar horários de atendimento
    const horarios = [
      { diaSemana: 1, horaInicio: '08:00', horaFim: '12:00' }, // Segunda
      { diaSemana: 1, horaInicio: '14:00', horaFim: '18:00' }, // Segunda tarde
      { diaSemana: 3, horaInicio: '08:00', horaFim: '12:00' }, // Quarta
      { diaSemana: 3, horaInicio: '14:00', horaFim: '18:00' }, // Quarta tarde
      { diaSemana: 5, horaInicio: '08:00', horaFim: '12:00' }, // Sexta
    ]

    for (const horario of horarios) {
      await prisma.horarioAtendimento.create({
        data: {
          medicoId: medico.id,
          ...horario,
          ativo: true
        }
      })
    }

    console.log('✅ Médico criado com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Senha: medico123')
    console.log('🏥 CRM: 123456/SP')
  }

  // Criar paciente de exemplo
  console.log('🤒 Criando paciente de exemplo...')
  
  const senhaPaciente = await bcrypt.hash('paciente123', 12)
  
  const usuarioPaciente = await prisma.usuario.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      senha: senhaPaciente,
      nome: 'Ana Santos',
      telefone: '(11) 96666-6666',
      tipo: 'PACIENTE',
      status: 'ATIVO'
    }
  })

  const paciente = await prisma.paciente.upsert({
    where: { usuarioId: usuarioPaciente.id },
    update: {},
    create: {
      usuarioId: usuarioPaciente.id,
      cpf: '123.456.789-00',
      dataNascimento: new Date('1985-05-15'),
      endereco: 'Rua das Palmeiras, 456 - São Paulo/SP',
      planoSaude: 'Unimed',
      contatoEmergencia: '(11) 95555-5555 - José Santos (marido)',
      observacoes: 'Alérgica a penicilina'
    }
  })

  console.log('✅ Paciente criado com sucesso!')
  console.log('📧 Email: <EMAIL>')
  console.log('🔑 Senha: paciente123')

  // Criar configurações do sistema
  console.log('⚙️ Criando configurações do sistema...')
  
  const configuracoes = [
    { chave: 'nome_clinica', valor: 'Agenteia Médicos', descricao: 'Nome da clínica/agência médica' },
    { chave: 'endereco_clinica', valor: 'Rua Principal, 1000 - Centro - São Paulo/SP', descricao: 'Endereço da clínica' },
    { chave: 'telefone_clinica', valor: '(11) 3333-3333', descricao: 'Telefone principal da clínica' },
    { chave: 'email_clinica', valor: '<EMAIL>', descricao: 'Email de contato da clínica' },
    { chave: 'horario_funcionamento', valor: 'Segunda a Sexta: 7h às 19h | Sábado: 7h às 12h', descricao: 'Horário de funcionamento' },
    { chave: 'tempo_consulta_padrao', valor: '30', descricao: 'Tempo padrão de consulta em minutos' },
    { chave: 'antecedencia_cancelamento', valor: '24', descricao: 'Antecedência mínima para cancelamento em horas' },
    { chave: 'lembrete_consulta_horas', valor: '24', descricao: 'Horas antes da consulta para enviar lembrete' }
  ]

  for (const config of configuracoes) {
    await prisma.configuracao.upsert({
      where: { chave: config.chave },
      update: { valor: config.valor },
      create: config
    })
  }

  console.log('✅ Configurações criadas com sucesso!')

  console.log('\n🎉 Seed concluído com sucesso!')
  console.log('\n📋 Resumo dos usuários criados:')
  console.log('👑 Admin: <EMAIL> / admin123')
  console.log('👥 Recepcionista: <EMAIL> / recepcao123')
  console.log('👨‍⚕️ Médico: <EMAIL> / medico123')
  console.log('🤒 Paciente: <EMAIL> / paciente123')
}

main()
  .catch((e) => {
    console.error('❌ Erro durante o seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
