import { Request, Response } from 'express'
import { MedicoService } from '../services/medicoService'
import { CreateMedicoDTO, UpdateMedicoDTO, MedicoFilters } from '../../../shared/src/types'
import { validarEmail, validarCRM, validarTelefone } from '../../../shared/src/utils'

const medicoService = new MedicoService()

export class MedicoController {
  /**
   * Criar novo médico
   */
  async create(req: Request, res: Response) {
    try {
      const data: CreateMedicoDTO = req.body

      // Validações básicas
      if (!data.usuario?.email || !data.usuario?.senha || !data.usuario?.nome || !data.crm) {
        return res.status(400).json({
          success: false,
          message: 'Email, senha, nome e CRM são obrigatórios'
        })
      }

      if (!validarEmail(data.usuario.email)) {
        return res.status(400).json({
          success: false,
          message: 'Email inválido'
        })
      }

      if (!validarCRM(data.crm)) {
        return res.status(400).json({
          success: false,
          message: 'CRM inválido. Use o formato: 123456/UF'
        })
      }

      if (data.usuario.telefone && !validarTelefone(data.usuario.telefone)) {
        return res.status(400).json({
          success: false,
          message: 'Telefone inválido'
        })
      }

      if (data.usuario.senha.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Senha deve ter pelo menos 6 caracteres'
        })
      }

      const medico = await medicoService.create(data)

      res.status(201).json({
        success: true,
        message: 'Médico criado com sucesso',
        data: medico
      })
    } catch (error) {
      console.error('Erro ao criar médico:', error)
      
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar médico por ID
   */
  async findById(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID do médico é obrigatório'
        })
      }

      const medico = await medicoService.findById(id)

      res.json({
        success: true,
        data: medico
      })
    } catch (error) {
      console.error('Erro ao buscar médico:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar médico do usuário atual
   */
  async findMyProfile(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Usuário não autenticado'
        })
      }

      const medico = await medicoService.findByUserId(req.user.userId)

      res.json({
        success: true,
        data: medico
      })
    } catch (error) {
      console.error('Erro ao buscar perfil do médico:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: 'Perfil de médico não encontrado para este usuário'
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Listar médicos com filtros
   */
  async findMany(req: Request, res: Response) {
    try {
      const filters: MedicoFilters = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        sortBy: req.query.sortBy as string || 'nome',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc',
        especialidadeId: req.query.especialidadeId as string,
        status: req.query.status as any,
        nome: req.query.nome as string
      }

      const resultado = await medicoService.findMany(filters)

      res.json({
        success: true,
        data: resultado.data,
        pagination: resultado.pagination
      })
    } catch (error) {
      console.error('Erro ao listar médicos:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Atualizar médico
   */
  async update(req: Request, res: Response) {
    try {
      const { id } = req.params
      const data: UpdateMedicoDTO = req.body

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID do médico é obrigatório'
        })
      }

      const medico = await medicoService.update(id, data)

      res.json({
        success: true,
        message: 'Médico atualizado com sucesso',
        data: medico
      })
    } catch (error) {
      console.error('Erro ao atualizar médico:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Atualizar perfil do médico atual
   */
  async updateMyProfile(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Usuário não autenticado'
        })
      }

      // Buscar médico pelo usuário
      const medicoAtual = await medicoService.findByUserId(req.user.userId)
      
      const data: UpdateMedicoDTO = req.body
      const medico = await medicoService.update(medicoAtual.id, data)

      res.json({
        success: true,
        message: 'Perfil atualizado com sucesso',
        data: medico
      })
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error)
      
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Desativar médico
   */
  async deactivate(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID do médico é obrigatório'
        })
      }

      const resultado = await medicoService.deactivate(id)

      res.json({
        success: true,
        message: resultado.message
      })
    } catch (error) {
      console.error('Erro ao desativar médico:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Ativar médico
   */
  async activate(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID do médico é obrigatório'
        })
      }

      const resultado = await medicoService.activate(id)

      res.json({
        success: true,
        message: resultado.message
      })
    } catch (error) {
      console.error('Erro ao ativar médico:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar médicos por especialidade
   */
  async findByEspecialidade(req: Request, res: Response) {
    try {
      const { especialidadeId } = req.params

      if (!especialidadeId) {
        return res.status(400).json({
          success: false,
          message: 'ID da especialidade é obrigatório'
        })
      }

      const medicos = await medicoService.findByEspecialidade(especialidadeId)

      res.json({
        success: true,
        data: medicos
      })
    } catch (error) {
      console.error('Erro ao buscar médicos por especialidade:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Obter estatísticas do médico
   */
  async getStats(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID do médico é obrigatório'
        })
      }

      const stats = await medicoService.getStats(id)

      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error)
      
      if (error instanceof Error && error.message === 'Médico não encontrado') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Obter estatísticas do médico atual
   */
  async getMyStats(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Usuário não autenticado'
        })
      }

      // Buscar médico pelo usuário
      const medicoAtual = await medicoService.findByUserId(req.user.userId)
      const stats = await medicoService.getStats(medicoAtual.id)

      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }
}
