{"name": "Agendamento Inteligente - Agenteia Médicos", "nodes": [{"parameters": {"httpMethod": "POST", "path": "agendar-consulta", "responseMode": "responseNode", "options": {}}, "id": "webhook-agendamento", "name": "Webhook Agendamento", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Você é um assistente médico especializado em triagem e agendamento. Analise os sintomas descritos pelo paciente e:\n\n1. Identifique a especialidade médica mais adequada\n2. Classifique a urgência (1-4, sendo 4 emergência)\n3. Sugira o tipo de consulta necessária\n4. Retorne APENAS um JSON com: {\"especialidade\": \"nome\", \"urgencia\": numero, \"tipo\": \"primeira_consulta|retorno|urgente\", \"observacoes\": \"texto\"}\n\nSintomas e informações do paciente:"}, {"role": "user", "content": "=Paciente: {{ $json.nome }}\nIdade: {{ $json.idade }}\nSintomas: {{ $json.sintomas }}\nHistórico: {{ $json.historico || 'Não informado' }}"}]}}, "id": "openai-triagem", "name": "IA - Triagem Médica", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.id, e.nome, e.valorConsulta FROM especialidades e WHERE LOWER(e.nome) LIKE LOWER('%{{ $json.especialidade }}%') LIMIT 1"}, "id": "buscar-especialidade", "name": "Buscar Especialidade", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT m.id, u.nome, m.crm FROM medicos m JOIN usuarios u ON m.usuarioId = u.id JOIN medico_especialidades me ON m.id = me.medicoId WHERE me.especialidadeId = '{{ $('buscar-especialidade').item.json.id }}' AND u.status = 'ATIVO' ORDER BY RANDOM() LIMIT 3"}, "id": "buscar-medicos", "name": "Buscar Médicos Disponíveis", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Baseado na urgência e disponibilidade, sugira a melhor data/hora para agendamento. Considere:\n- Urgência 4: <PERSON><PERSON> ou aman<PERSON>ã\n- Urgência 3: Próximos 3 dias\n- Urgência 2: Próxima semana\n- Urgência 1: Próximas 2 semanas\n\nRetorne JSON: {\"dataHora\": \"YYYY-MM-DD HH:mm\", \"medicoId\": \"id\", \"justificativa\": \"texto\"}"}, {"role": "user", "content": "=Urgência: {{ $('openai-triagem').item.json.urgencia }}\nMédicos disponíveis: {{ JSON.stringify($('buscar-medicos').all()) }}\nData atual: {{ $now.format('YYYY-MM-DD HH:mm') }}"}]}}, "id": "sugerir-agendamento", "name": "IA - Sugerir <PERSON>ndamento", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:3001/api/consultas", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.API_TOKEN }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "pacienteId", "value": "={{ $('webhook-agendamento').item.json.pacienteId }}"}, {"name": "medicoId", "value": "={{ $('sugerir-agendamento').item.json.medicoId }}"}, {"name": "especialidadeId", "value": "={{ $('buscar-especialidade').item.json.id }}"}, {"name": "dataHora", "value": "={{ $('sugerir-agendamento').item.json.dataHora }}"}, {"name": "observacoes", "value": "=Agendamento automático via IA. {{ $('openai-triagem').item.json.observacoes }}"}]}}, "id": "criar-consulta", "name": "C<PERSON>r Consulta no Sistema", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Crie uma mensagem de confirmação de agendamento amigável e profissional para WhatsApp. Inclua:\n- Saudação personalizada\n- Detalhes da consulta\n- Instruções de preparação se necessário\n- Contato para dúvidas\n\nMantenha tom acolhedor e profissional."}, {"role": "user", "content": "=Paciente: {{ $('webhook-agendamento').item.json.nome }}\nMédico: {{ $('buscar-medicos').item.json.nome }}\nEspecialidade: {{ $('buscar-especialidade').item.json.nome }}\nData/Hora: {{ $('sugerir-agendamento').item.json.dataHora }}\nValor: R$ {{ $('buscar-especialidade').item.json.valorConsulta }}"}]}}, "id": "gerar-mensagem", "name": "IA - <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v17.0/{{ $vars.WHATSAPP_PHONE_ID }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.WHATSAPP_TOKEN }}"}]}, "sendBody": true, "contentType": "json", "body": "={\n  \"messaging_product\": \"whatsapp\",\n  \"to\": \"{{ $('webhook-agendamento').item.json.telefone }}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"{{ $('gerar-mensagem').item.json }}\"\n  }\n}"}, "id": "enviar-whatsapp", "name": "Enviar WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Consulta agendada com sucesso!\",\n  \"consulta\": {\n    \"id\": \"{{ $('criar-consulta').item.json.data.id }}\",\n    \"dataHora\": \"{{ $('sugerir-agendamento').item.json.dataHora }}\",\n    \"medico\": \"{{ $('buscar-medicos').item.json.nome }}\",\n    \"especialidade\": \"{{ $('buscar-especialidade').item.json.nome }}\",\n    \"valor\": \"{{ $('buscar-especialidade').item.json.valorConsulta }}\"\n  }\n}"}, "id": "resposta-webhook", "name": "Resposta Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 300]}], "connections": {"Webhook Agendamento": {"main": [[{"node": "IA - Triagem Médica", "type": "main", "index": 0}]]}, "IA - Triagem Médica": {"main": [[{"node": "Buscar Especialidade", "type": "main", "index": 0}]]}, "Buscar Especialidade": {"main": [[{"node": "Buscar Médicos Disponíveis", "type": "main", "index": 0}]]}, "Buscar Médicos Disponíveis": {"main": [[{"node": "IA - Sugerir <PERSON>ndamento", "type": "main", "index": 0}]]}, "IA - Sugerir Agendamento": {"main": [[{"node": "C<PERSON>r Consulta no Sistema", "type": "main", "index": 0}]]}, "Criar Consulta no Sistema": {"main": [[{"node": "IA - <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "IA - Gerar Mensagem": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Enviar WhatsApp": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Sao_Paulo"}, "versionId": "1"}