import { Request, Response, NextFunction } from 'express'
import { AuthService, TokenPayload } from '../services/authService'
import { TipoUsuario } from '../../../shared/src/types'

// Estender interface Request para incluir dados do usuário
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload
    }
  }
}

const authService = new AuthService()

/**
 * Middleware para verificar se usuário está autenticado
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Token de acesso requerido'
      })
    }

    // Verificar e decodificar token
    const decoded = authService.verifyToken(token)
    
    // Verificar se usuário ainda existe e está ativo
    const usuario = await authService.getUserById(decoded.userId)
    
    if (!usuario) {
      return res.status(401).json({
        success: false,
        message: 'Usuário não encontrado'
      })
    }

    // Adicionar dados do usuário à requisição
    req.user = decoded

    next()
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Token inválido'
    })
  }
}

/**
 * Middleware para verificar permissões específicas
 */
export const requirePermissions = (allowedTypes: TipoUsuario[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Usuário não autenticado'
      })
    }

    const hasPermission = authService.hasPermission(req.user.tipo, allowedTypes)

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado. Permissões insuficientes'
      })
    }

    next()
  }
}

/**
 * Middleware para verificar se usuário é administrador
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Usuário não autenticado'
    })
  }

  if (!authService.isAdmin(req.user.tipo)) {
    return res.status(403).json({
      success: false,
      message: 'Acesso negado. Apenas administradores'
    })
  }

  next()
}

/**
 * Middleware para verificar se usuário pode acessar recurso específico
 */
export const requireResourceAccess = (getResourceOwnerId: (req: Request) => string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Usuário não autenticado'
      })
    }

    const resourceOwnerId = getResourceOwnerId(req)
    const canAccess = authService.canAccessResource(
      req.user.tipo,
      resourceOwnerId,
      req.user.userId
    )

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado. Você não pode acessar este recurso'
      })
    }

    next()
  }
}

/**
 * Middleware para verificar se usuário é médico
 */
export const requireMedico = requirePermissions([
  TipoUsuario.ADMINISTRADOR,
  TipoUsuario.MEDICO
])

/**
 * Middleware para verificar se usuário é recepcionista ou admin
 */
export const requireRecepcionista = requirePermissions([
  TipoUsuario.ADMINISTRADOR,
  TipoUsuario.RECEPCIONISTA
])

/**
 * Middleware para verificar se usuário é médico ou recepcionista
 */
export const requireMedicoOrRecepcionista = requirePermissions([
  TipoUsuario.ADMINISTRADOR,
  TipoUsuario.MEDICO,
  TipoUsuario.RECEPCIONISTA
])

/**
 * Middleware para verificar se usuário é paciente (para acessar próprios dados)
 */
export const requirePaciente = requirePermissions([
  TipoUsuario.ADMINISTRADOR,
  TipoUsuario.PACIENTE
])

/**
 * Middleware opcional de autenticação (não falha se não houver token)
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = authService.verifyToken(token)
      req.user = decoded
    }

    next()
  } catch (error) {
    // Ignorar erro e continuar sem autenticação
    next()
  }
}

/**
 * Middleware para log de ações (auditoria)
 */
export const logAction = (action: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.userId || 'anonymous'
    const userType = req.user?.tipo || 'unknown'
    const timestamp = new Date().toISOString()
    
    console.log(`[${timestamp}] ${action} - User: ${userId} (${userType}) - IP: ${req.ip}`)
    
    next()
  }
}

/**
 * Middleware para rate limiting por usuário
 */
const userRequestCounts = new Map<string, { count: number; resetTime: number }>()

export const rateLimitByUser = (maxRequests: number, windowMs: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.userId || req.ip
    const now = Date.now()
    
    const userLimit = userRequestCounts.get(userId)
    
    if (!userLimit || now > userLimit.resetTime) {
      // Reset ou primeira requisição
      userRequestCounts.set(userId, {
        count: 1,
        resetTime: now + windowMs
      })
      return next()
    }
    
    if (userLimit.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Muitas requisições. Tente novamente mais tarde.'
      })
    }
    
    userLimit.count++
    next()
  }
}

/**
 * Middleware para validar se usuário pode modificar dados de outro usuário
 */
export const canModifyUser = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Usuário não autenticado'
    })
  }

  const targetUserId = req.params.id || req.body.userId
  
  // Admin pode modificar qualquer usuário
  if (authService.isAdmin(req.user.tipo)) {
    return next()
  }
  
  // Usuário pode modificar apenas seus próprios dados
  if (req.user.userId === targetUserId) {
    return next()
  }
  
  return res.status(403).json({
    success: false,
    message: 'Você não pode modificar dados de outro usuário'
  })
}
