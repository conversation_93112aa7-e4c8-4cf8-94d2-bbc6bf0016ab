// Tipos compartilhados entre frontend e backend

// Enums
export enum TipoUsuario {
  ADMINISTRADOR = 'ADMINISTRADOR',
  MEDICO = 'MEDICO',
  RECEPCIONISTA = 'RECEPCIONISTA',
  PACIENTE = 'PACIENTE'
}

export enum StatusConsulta {
  AGENDADA = 'AGENDADA',
  CONFIRMADA = 'CONFIRMADA',
  EM_ANDAMENTO = 'EM_ANDAMENTO',
  CONCLUIDA = 'CONCLUIDA',
  CANCELADA = 'CANCELADA',
  FALTOU = 'FALTOU'
}

export enum StatusUsuario {
  ATIVO = 'ATIVO',
  INATIVO = 'INATIVO',
  SUSPENSO = 'SUSPENSO'
}

// Interfaces base
export interface Usuario {
  id: string
  email: string
  nome: string
  telefone?: string
  tipo: TipoUsuario
  status: StatusUsuario
  criadoEm: Date
  atualizadoEm: Date
}

export interface Especialidade {
  id: string
  nome: string
  descricao?: string
  valorConsulta: number
  criadoEm: Date
  atualizadoEm: Date
}

export interface Medico {
  id: string
  usuarioId: string
  crm: string
  biografia?: string
  foto?: string
  endereco?: string
  criadoEm: Date
  atualizadoEm: Date
  usuario?: Usuario
  especialidades?: MedicoEspecialidade[]
  horarios?: HorarioAtendimento[]
}

export interface MedicoEspecialidade {
  id: string
  medicoId: string
  especialidadeId: string
  medico?: Medico
  especialidade?: Especialidade
}

export interface HorarioAtendimento {
  id: string
  medicoId: string
  diaSemana: number
  horaInicio: string
  horaFim: string
  ativo: boolean
  criadoEm: Date
  atualizadoEm: Date
}

export interface Paciente {
  id: string
  usuarioId: string
  cpf: string
  dataNascimento: Date
  endereco?: string
  planoSaude?: string
  contatoEmergencia?: string
  observacoes?: string
  criadoEm: Date
  atualizadoEm: Date
  usuario?: Usuario
}

export interface Consulta {
  id: string
  pacienteId: string
  medicoId: string
  especialidadeId: string
  dataHora: Date
  status: StatusConsulta
  observacoes?: string
  valor?: number
  criadoEm: Date
  atualizadoEm: Date
  paciente?: Paciente
  medico?: Medico
  especialidade?: Especialidade
}

export interface HistoricoMedico {
  id: string
  pacienteId: string
  consultaId?: string
  titulo: string
  descricao: string
  prescricao?: string
  exames?: string
  anexos?: string
  criadoEm: Date
  atualizadoEm: Date
}

// DTOs para criação
export interface CreateUsuarioDTO {
  email: string
  senha: string
  nome: string
  telefone?: string
  tipo: TipoUsuario
}

export interface CreateMedicoDTO {
  usuario: CreateUsuarioDTO
  crm: string
  biografia?: string
  endereco?: string
  especialidades: string[]
}

export interface CreatePacienteDTO {
  usuario: CreateUsuarioDTO
  cpf: string
  dataNascimento: Date
  endereco?: string
  planoSaude?: string
  contatoEmergencia?: string
  observacoes?: string
}

export interface CreateConsultaDTO {
  pacienteId: string
  medicoId: string
  especialidadeId: string
  dataHora: Date
  observacoes?: string
}

export interface CreateEspecialidadeDTO {
  nome: string
  descricao?: string
  valorConsulta: number
}

// DTOs para atualização
export interface UpdateUsuarioDTO {
  email?: string
  nome?: string
  telefone?: string
  status?: StatusUsuario
}

export interface UpdateMedicoDTO {
  biografia?: string
  endereco?: string
  especialidades?: string[]
}

export interface UpdatePacienteDTO {
  endereco?: string
  planoSaude?: string
  contatoEmergencia?: string
  observacoes?: string
}

export interface UpdateConsultaDTO {
  dataHora?: Date
  status?: StatusConsulta
  observacoes?: string
  valor?: number
}

// DTOs de resposta
export interface LoginResponseDTO {
  token: string
  usuario: Usuario
  medico?: Medico
  paciente?: Paciente
}

export interface ApiResponseDTO<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// Tipos para filtros e paginação
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ConsultaFilters extends PaginationParams {
  medicoId?: string
  pacienteId?: string
  especialidadeId?: string
  status?: StatusConsulta
  dataInicio?: Date
  dataFim?: Date
}

export interface MedicoFilters extends PaginationParams {
  especialidadeId?: string
  status?: StatusUsuario
  nome?: string
}

export interface PacienteFilters extends PaginationParams {
  nome?: string
  cpf?: string
  status?: StatusUsuario
}

// Tipos para dashboard
export interface DashboardStats {
  totalConsultas: number
  consultasHoje: number
  consultasSemana: number
  consultasMes: number
  totalPacientes: number
  totalMedicos: number
  receitaMes: number
  taxaOcupacao: number
}

export interface ConsultasPorDia {
  data: string
  total: number
}

export interface ConsultasPorEspecialidade {
  especialidade: string
  total: number
  receita: number
}
