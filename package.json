{"name": "agenteia-medicos", "version": "1.0.0", "description": "Sistema completo para gerenciamento de agência médica", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "db:migrate": "cd backend && npx prisma migrate dev", "db:generate": "cd backend && npx prisma generate", "db:studio": "cd backend && npx prisma studio", "setup": "npm install && cd frontend && npm install && cd ../backend && npm install", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint"}, "keywords": ["medicina", "agendamento", "consultas", "medicos", "pacientes", "clinica"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "shared"]}