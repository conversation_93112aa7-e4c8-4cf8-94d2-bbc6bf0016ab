# Dockerfile para Backend Node.js (Desenvolvimento)

# Usar imagem oficial do Node.js
FROM node:18-alpine

# Definir diretório de trabalho
WORKDIR /app

# Copiar package.json e tsconfig.json do shared e backend
COPY shared/package*.json ./shared/
COPY shared/tsconfig.json ./shared/
COPY backend/package*.json ./backend/
COPY backend/tsconfig.json ./backend/

# Copiar todo o código fonte do shared primeiro
COPY shared/src ./shared/src

# Instalar dependências e fazer build do shared
WORKDIR /app/shared
RUN npm install
RUN npm run build

# Instalar dependências do backend
WORKDIR /app/backend
RUN npm install

# Copiar código fonte do backend
WORKDIR /app/backend
COPY backend/src ./src
COPY backend/prisma ./prisma

# Gerar cliente Prisma
RUN npx prisma generate

# Voltar para diretório do backend
WORKDIR /app/backend

# Expor porta
EXPOSE 3001

# Comando para desenvolvimento
CMD ["npm", "run", "dev"]
