import { Request, Response } from "express";
import { AuthService } from "../services/authService";
import { validarEmail } from "../../../shared/src/utils";
import { TipoUsuario } from "../../../shared/src/types";

const authService = new AuthService();

/**
 * Controller para operações de autenticação
 */
export class AuthController {
  /**
   * Login de usuário
   */
  async login(req: Request, res: Response) {
    try {
      const { email, senha } = req.body;

      // Validações básicas
      if (!email || !senha) {
        return res.status(400).json({
          success: false,
          message: "Email e senha são obrigatórios",
        });
      }

      if (!validarEmail(email)) {
        return res.status(400).json({
          success: false,
          message: "Email inválido",
        });
      }

      // Realizar login
      const result = await authService.login({ email, senha });

      res.json({
        success: true,
        message: "Login realizado com sucesso",
        data: result,
      });
    } catch (error) {
      console.error("Erro no login:", error);

      res.status(401).json({
        success: false,
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
      });
    }
  }

  /**
   * Registro de novo usuário
   */
  async register(req: Request, res: Response) {
    try {
      const { email, senha, nome, telefone, tipo } = req.body;

      // Validações básicas
      if (!email || !senha || !nome || !tipo) {
        return res.status(400).json({
          success: false,
          message: "Email, senha, nome e tipo são obrigatórios",
        });
      }

      if (!validarEmail(email)) {
        return res.status(400).json({
          success: false,
          message: "Email inválido",
        });
      }

      if (senha.length < 6) {
        return res.status(400).json({
          success: false,
          message: "Senha deve ter pelo menos 6 caracteres",
        });
      }

      if (!Object.values(TipoUsuario).includes(tipo)) {
        return res.status(400).json({
          success: false,
          message: "Tipo de usuário inválido",
        });
      }

      // Realizar registro
      const result = await authService.register({
        email,
        senha,
        nome,
        telefone,
        tipo,
      });

      res.status(201).json({
        success: true,
        message: "Usuário registrado com sucesso",
        data: result,
      });
    } catch (error) {
      console.error("Erro no registro:", error);

      res.status(400).json({
        success: false,
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
      });
    }
  }

  /**
   * Obter dados do usuário atual
   */
  async me(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Usuário não autenticado",
        });
      }

      const usuario = await authService.getUserById(req.user.userId);

      res.json({
        success: true,
        data: { usuario },
      });
    } catch (error) {
      console.error("Erro ao buscar usuário:", error);

      res.status(500).json({
        success: false,
        message: "Erro interno do servidor",
      });
    }
  }

  /**
   * Atualizar senha do usuário
   */
  async updatePassword(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Usuário não autenticado",
        });
      }

      const { senhaAtual, novaSenha } = req.body;

      if (!senhaAtual || !novaSenha) {
        return res.status(400).json({
          success: false,
          message: "Senha atual e nova senha são obrigatórias",
        });
      }

      if (novaSenha.length < 6) {
        return res.status(400).json({
          success: false,
          message: "Nova senha deve ter pelo menos 6 caracteres",
        });
      }

      const result = await authService.updatePassword(
        req.user.userId,
        senhaAtual,
        novaSenha
      );

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      console.error("Erro ao atualizar senha:", error);

      res.status(400).json({
        success: false,
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
      });
    }
  }

  /**
   * Solicitar reset de senha
   */
  async requestPasswordReset(req: Request, res: Response) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({
          success: false,
          message: "Email é obrigatório",
        });
      }

      if (!validarEmail(email)) {
        return res.status(400).json({
          success: false,
          message: "Email inválido",
        });
      }

      const result = await authService.requestPasswordReset(email);

      res.json({
        success: true,
        message: result.message,
        // Em produção, remover o resetToken
        ...(process.env.NODE_ENV === "development" && {
          resetToken: result.resetToken,
        }),
      });
    } catch (error) {
      console.error("Erro ao solicitar reset de senha:", error);

      res.status(500).json({
        success: false,
        message: "Erro interno do servidor",
      });
    }
  }

  /**
   * Reset de senha com token
   */
  async resetPassword(req: Request, res: Response) {
    try {
      const { token, novaSenha } = req.body;

      if (!token || !novaSenha) {
        return res.status(400).json({
          success: false,
          message: "Token e nova senha são obrigatórios",
        });
      }

      if (novaSenha.length < 6) {
        return res.status(400).json({
          success: false,
          message: "Nova senha deve ter pelo menos 6 caracteres",
        });
      }

      const result = await authService.resetPassword(token, novaSenha);

      res.json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      console.error("Erro ao resetar senha:", error);

      res.status(400).json({
        success: false,
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
      });
    }
  }

  /**
   * Logout (invalidar token - implementação simples)
   */
  async logout(req: Request, res: Response) {
    try {
      // Em uma implementação mais robusta, você manteria uma blacklist de tokens
      // Por enquanto, apenas retornamos sucesso (o frontend deve remover o token)

      res.json({
        success: true,
        message: "Logout realizado com sucesso",
      });
    } catch (error) {
      console.error("Erro no logout:", error);

      res.status(500).json({
        success: false,
        message: "Erro interno do servidor",
      });
    }
  }

  /**
   * Verificar se token é válido
   */
  async verifyToken(req: Request, res: Response) {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          success: false,
          message: "Token é obrigatório",
        });
      }

      const decoded = authService.verifyToken(token);
      const usuario = await authService.getUserById(decoded.userId);

      res.json({
        success: true,
        message: "Token válido",
        data: {
          valid: true,
          usuario,
        },
      });
    } catch (error) {
      res.json({
        success: true,
        data: {
          valid: false,
          message: "Token inválido",
        },
      });
    }
  }

  /**
   * Refresh token (gerar novo token)
   */
  async refreshToken(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Usuário não autenticado",
        });
      }

      // Gerar novo token
      const novoToken = authService.generateToken({
        userId: req.user.userId,
        email: req.user.email,
        tipo: req.user.tipo,
      });

      res.json({
        success: true,
        message: "Token renovado com sucesso",
        data: {
          token: novoToken,
        },
      });
    } catch (error) {
      console.error("Erro ao renovar token:", error);

      res.status(500).json({
        success: false,
        message: "Erro interno do servidor",
      });
    }
  }
}
