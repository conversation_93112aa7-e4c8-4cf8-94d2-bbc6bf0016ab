import { Request, Response } from 'express'
import { EspecialidadeService } from '../services/especialidadeService'
import { CreateEspecialidadeDTO, PaginationParams } from '../../../shared/src/types'

const especialidadeService = new EspecialidadeService()

export class EspecialidadeController {
  /**
   * Criar nova especialidade
   */
  async create(req: Request, res: Response) {
    try {
      const data: CreateEspecialidadeDTO = req.body

      // Validações básicas
      if (!data.nome || !data.valorConsulta) {
        return res.status(400).json({
          success: false,
          message: 'Nome e valor da consulta são obrigatórios'
        })
      }

      if (typeof data.valorConsulta !== 'number' || data.valorConsulta <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Valor da consulta deve ser um número maior que zero'
        })
      }

      const especialidade = await especialidadeService.create(data)

      res.status(201).json({
        success: true,
        message: 'Especialidade criada com sucesso',
        data: especialidade
      })
    } catch (error) {
      console.error('Erro ao criar especialidade:', error)
      
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar especialidade por ID
   */
  async findById(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID da especialidade é obrigatório'
        })
      }

      const especialidade = await especialidadeService.findById(id)

      res.json({
        success: true,
        data: especialidade
      })
    } catch (error) {
      console.error('Erro ao buscar especialidade:', error)
      
      if (error instanceof Error && error.message === 'Especialidade não encontrada') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Listar especialidades com paginação
   */
  async findMany(req: Request, res: Response) {
    try {
      const params: PaginationParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        sortBy: req.query.sortBy as string || 'nome',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc'
      }

      const resultado = await especialidadeService.findMany(params)

      res.json({
        success: true,
        data: resultado.data,
        pagination: resultado.pagination
      })
    } catch (error) {
      console.error('Erro ao listar especialidades:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Listar todas as especialidades (sem paginação)
   */
  async findAll(req: Request, res: Response) {
    try {
      const especialidades = await especialidadeService.findAll()

      res.json({
        success: true,
        data: especialidades
      })
    } catch (error) {
      console.error('Erro ao listar especialidades:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Atualizar especialidade
   */
  async update(req: Request, res: Response) {
    try {
      const { id } = req.params
      const data: Partial<CreateEspecialidadeDTO> = req.body

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID da especialidade é obrigatório'
        })
      }

      if (data.valorConsulta !== undefined && (typeof data.valorConsulta !== 'number' || data.valorConsulta <= 0)) {
        return res.status(400).json({
          success: false,
          message: 'Valor da consulta deve ser um número maior que zero'
        })
      }

      const especialidade = await especialidadeService.update(id, data)

      res.json({
        success: true,
        message: 'Especialidade atualizada com sucesso',
        data: especialidade
      })
    } catch (error) {
      console.error('Erro ao atualizar especialidade:', error)
      
      if (error instanceof Error && error.message === 'Especialidade não encontrada') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Deletar especialidade
   */
  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID da especialidade é obrigatório'
        })
      }

      const resultado = await especialidadeService.delete(id)

      res.json({
        success: true,
        message: resultado.message
      })
    } catch (error) {
      console.error('Erro ao deletar especialidade:', error)
      
      if (error instanceof Error && error.message === 'Especialidade não encontrada') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar especialidades com médicos disponíveis
   */
  async findWithMedicos(req: Request, res: Response) {
    try {
      const especialidades = await especialidadeService.findWithMedicos()

      res.json({
        success: true,
        data: especialidades
      })
    } catch (error) {
      console.error('Erro ao buscar especialidades com médicos:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Obter estatísticas da especialidade
   */
  async getStats(req: Request, res: Response) {
    try {
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID da especialidade é obrigatório'
        })
      }

      const stats = await especialidadeService.getStats(id)

      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error)
      
      if (error instanceof Error && error.message === 'Especialidade não encontrada') {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }

  /**
   * Buscar especialidades mais populares
   */
  async findMostPopular(req: Request, res: Response) {
    try {
      const limit = parseInt(req.query.limit as string) || 5

      const especialidades = await especialidadeService.findMostPopular(limit)

      res.json({
        success: true,
        data: especialidades
      })
    } catch (error) {
      console.error('Erro ao buscar especialidades populares:', error)
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      })
    }
  }
}
