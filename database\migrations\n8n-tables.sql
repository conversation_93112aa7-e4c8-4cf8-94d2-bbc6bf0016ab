-- Tabelas adicionais para workflows N8N
-- Execute este script após as migrações principais do Prisma

-- Tabela para registrar conversas do WhatsApp
CREATE TABLE IF NOT EXISTS conversas_whatsapp (
    id SERIAL PRIMARY KEY,
    telefone VARCHAR(20) NOT NULL,
    mensagem TEXT NOT NULL,
    resposta_ia TEXT,
    urgencia INTEGER DEFAULT 1,
    paciente_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processado BOOLEAN DEFAULT FALSE,
    
    -- Índices para performance
    INDEX idx_telefone (telefone),
    INDEX idx_timestamp (timestamp),
    INDEX idx_paciente_id (paciente_id)
);

-- Tabela para registrar lembretes enviados
CREATE TABLE IF NOT EXISTS lembretes_enviados (
    id SERIAL PRIMARY KEY,
    consulta_id VARCHAR(255) NOT NULL,
    tipo VARCHAR(50) NOT NULL, -- 'lembrete_24h', 'lembrete_1h', 'confirmacao'
    canal VARCHAR(50) NOT NULL, -- 'whatsapp', 'email', 'sms', 'whatsapp_email'
    telefone VARCHAR(20),
    email VARCHAR(255),
    mensagem TEXT,
    enviado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'enviado', -- 'enviado', 'entregue', 'lido', 'erro'
    erro_detalhes TEXT,
    
    -- Índices para performance
    INDEX idx_consulta_id (consulta_id),
    INDEX idx_tipo (tipo),
    INDEX idx_enviado_em (enviado_em),
    INDEX idx_status (status),
    
    -- Evitar duplicatas
    UNIQUE KEY unique_lembrete (consulta_id, tipo, canal)
);

-- Tabela para análises de feedback
CREATE TABLE IF NOT EXISTS feedback_analises (
    id SERIAL PRIMARY KEY,
    consulta_id VARCHAR(255),
    paciente_nome VARCHAR(255) NOT NULL,
    medico_nome VARCHAR(255) NOT NULL,
    especialidade VARCHAR(255) NOT NULL,
    nota INTEGER NOT NULL CHECK (nota >= 1 AND nota <= 5),
    comentario TEXT,
    recomendaria BOOLEAN,
    
    -- Análise da IA
    sentimento VARCHAR(20) NOT NULL, -- 'positivo', 'neutro', 'negativo'
    score INTEGER NOT NULL CHECK (score >= 1 AND score <= 10),
    categoria VARCHAR(50) NOT NULL, -- 'atendimento', 'medico', 'infraestrutura', etc.
    urgencia VARCHAR(20) NOT NULL, -- 'baixa', 'media', 'alta'
    resumo TEXT,
    acoes_sugeridas JSON,
    palavras_chave JSON,
    
    analisado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processado BOOLEAN DEFAULT FALSE,
    
    -- Índices para performance
    INDEX idx_consulta_id (consulta_id),
    INDEX idx_sentimento (sentimento),
    INDEX idx_categoria (categoria),
    INDEX idx_urgencia (urgencia),
    INDEX idx_analisado_em (analisado_em),
    INDEX idx_score (score)
);

-- Tabela para logs de execução dos workflows
CREATE TABLE IF NOT EXISTS workflow_logs (
    id SERIAL PRIMARY KEY,
    workflow_name VARCHAR(255) NOT NULL,
    execution_id VARCHAR(255),
    status VARCHAR(50) NOT NULL, -- 'success', 'error', 'running'
    input_data JSON,
    output_data JSON,
    error_message TEXT,
    execution_time INTEGER, -- em milissegundos
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    finished_at TIMESTAMP,
    
    -- Índices para performance
    INDEX idx_workflow_name (workflow_name),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    INDEX idx_execution_id (execution_id)
);

-- Tabela para configurações dos workflows
CREATE TABLE IF NOT EXISTS workflow_config (
    id SERIAL PRIMARY KEY,
    chave VARCHAR(255) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo VARCHAR(50) DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    categoria VARCHAR(100) DEFAULT 'geral',
    ativo BOOLEAN DEFAULT TRUE,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_chave (chave),
    INDEX idx_categoria (categoria),
    INDEX idx_ativo (ativo)
);

-- Tabela para métricas dos workflows
CREATE TABLE IF NOT EXISTS workflow_metricas (
    id SERIAL PRIMARY KEY,
    data_referencia DATE NOT NULL,
    workflow_name VARCHAR(255) NOT NULL,
    execucoes_total INTEGER DEFAULT 0,
    execucoes_sucesso INTEGER DEFAULT 0,
    execucoes_erro INTEGER DEFAULT 0,
    tempo_medio_execucao INTEGER DEFAULT 0, -- em milissegundos
    
    -- Métricas específicas
    mensagens_whatsapp INTEGER DEFAULT 0,
    emails_enviados INTEGER DEFAULT 0,
    lembretes_enviados INTEGER DEFAULT 0,
    feedbacks_analisados INTEGER DEFAULT 0,
    consultas_agendadas INTEGER DEFAULT 0,
    
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices para performance
    INDEX idx_data_referencia (data_referencia),
    INDEX idx_workflow_name (workflow_name),
    UNIQUE KEY unique_metrica (data_referencia, workflow_name)
);

-- Tabela para templates de mensagens
CREATE TABLE IF NOT EXISTS mensagem_templates (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) UNIQUE NOT NULL,
    tipo VARCHAR(50) NOT NULL, -- 'whatsapp', 'email', 'sms'
    categoria VARCHAR(100) NOT NULL, -- 'lembrete', 'confirmacao', 'agradecimento'
    assunto VARCHAR(255), -- para emails
    corpo TEXT NOT NULL,
    variaveis JSON, -- lista de variáveis disponíveis
    ativo BOOLEAN DEFAULT TRUE,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nome (nome),
    INDEX idx_tipo (tipo),
    INDEX idx_categoria (categoria),
    INDEX idx_ativo (ativo)
);

-- Inserir configurações padrão
INSERT INTO workflow_config (chave, valor, descricao, categoria) VALUES
('whatsapp_ativo', 'true', 'Ativar envio de mensagens WhatsApp', 'whatsapp'),
('email_ativo', 'true', 'Ativar envio de emails', 'email'),
('lembrete_24h_ativo', 'true', 'Ativar lembretes 24h antes', 'lembretes'),
('lembrete_1h_ativo', 'true', 'Ativar lembretes 1h antes', 'lembretes'),
('feedback_auto_resposta', 'true', 'Resposta automática para feedbacks', 'feedback'),
('triagem_ia_ativo', 'true', 'Ativar triagem automática com IA', 'ia'),
('agendamento_auto_ativo', 'false', 'Ativar agendamento automático', 'agendamento'),
('horario_funcionamento_inicio', '07:00', 'Horário de início do funcionamento', 'geral'),
('horario_funcionamento_fim', '19:00', 'Horário de fim do funcionamento', 'geral'),
('dias_funcionamento', '[1,2,3,4,5,6]', 'Dias da semana de funcionamento (0=Dom, 6=Sab)', 'geral')
ON DUPLICATE KEY UPDATE valor = VALUES(valor);

-- Inserir templates padrão
INSERT INTO mensagem_templates (nome, tipo, categoria, corpo, variaveis) VALUES
('lembrete_24h_whatsapp', 'whatsapp', 'lembrete', 
'🏥 Olá {{paciente_nome}}! Lembrete: consulta amanhã às {{hora}} com {{medico_nome}} ({{especialidade}}). Local: Agenteia Médicos. Dúvidas: (11) 3333-3333',
'["paciente_nome", "hora", "medico_nome", "especialidade"]'),

('lembrete_1h_whatsapp', 'whatsapp', 'lembrete',
'🏥 Olá {{paciente_nome}}! Sua consulta com {{medico_nome}} ({{especialidade}}) é em 1 hora. Não se esqueça! 📍 Agenteia Médicos',
'["paciente_nome", "medico_nome", "especialidade"]'),

('confirmacao_agendamento', 'whatsapp', 'confirmacao',
'✅ Consulta agendada! {{paciente_nome}}, sua consulta com {{medico_nome}} está marcada para {{data_hora}}. Valor: R$ {{valor}}. Agenteia Médicos',
'["paciente_nome", "medico_nome", "data_hora", "valor"]'),

('agradecimento_feedback', 'whatsapp', 'agradecimento',
'🙏 Obrigado pelo feedback, {{paciente_nome}}! Ficamos felizes que tenha gostado do atendimento com {{medico_nome}}. Conte sempre conosco! 💙',
'["paciente_nome", "medico_nome"]')
ON DUPLICATE KEY UPDATE corpo = VALUES(corpo);

-- Criar função para atualizar métricas diárias
DELIMITER //
CREATE OR REPLACE FUNCTION atualizar_metricas_diarias()
RETURNS VOID AS $$
BEGIN
    INSERT INTO workflow_metricas (
        data_referencia, 
        workflow_name, 
        execucoes_total, 
        execucoes_sucesso, 
        execucoes_erro,
        tempo_medio_execucao
    )
    SELECT 
        DATE(started_at) as data_referencia,
        workflow_name,
        COUNT(*) as execucoes_total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as execucoes_sucesso,
        SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as execucoes_erro,
        AVG(execution_time) as tempo_medio_execucao
    FROM workflow_logs 
    WHERE DATE(started_at) = CURRENT_DATE - INTERVAL 1 DAY
    GROUP BY DATE(started_at), workflow_name
    ON DUPLICATE KEY UPDATE
        execucoes_total = VALUES(execucoes_total),
        execucoes_sucesso = VALUES(execucoes_sucesso),
        execucoes_erro = VALUES(execucoes_erro),
        tempo_medio_execucao = VALUES(tempo_medio_execucao),
        atualizado_em = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;
//
DELIMITER ;

-- Comentários para documentação
COMMENT ON TABLE conversas_whatsapp IS 'Registra todas as conversas recebidas via WhatsApp para análise e histórico';
COMMENT ON TABLE lembretes_enviados IS 'Controla os lembretes enviados para evitar duplicatas e monitorar entregas';
COMMENT ON TABLE feedback_analises IS 'Armazena análises de feedback processadas pela IA com sentimentos e ações sugeridas';
COMMENT ON TABLE workflow_logs IS 'Log de execução de todos os workflows para monitoramento e debugging';
COMMENT ON TABLE workflow_config IS 'Configurações dinâmicas dos workflows que podem ser alteradas sem redeploy';
COMMENT ON TABLE workflow_metricas IS 'Métricas agregadas diárias para dashboards e relatórios de performance';
COMMENT ON TABLE mensagem_templates IS 'Templates de mensagens reutilizáveis para diferentes canais e situações';
