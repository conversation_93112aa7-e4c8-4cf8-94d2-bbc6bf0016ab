# Guia de Instalação N8N - Agenteia Médicos

## 🚀 Instalação Rápida

### 1. Adicionar N8N ao Docker Compose

Adicione ao seu `docker-compose.yml`:

```yaml
  # N8N Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    container_name: agenteia-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678
      - GENERIC_TIMEZONE=America/Sao_Paulo
      
      # Database
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password
      
      # Executions
      - EXECUTIONS_PROCESS=main
      - EXECUTIONS_DATA_SAVE_ON_ERROR=all
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=all
      - EXECUTIONS_DATA_MAX_AGE=168
      
      # Security
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=agenteia123
      
      # API Keys (configurar com suas chaves)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - WHATSAPP_TOKEN=${WHATSAPP_TOKEN}
      - WHATSAPP_PHONE_ID=${WHATSAPP_PHONE_ID}
      - API_TOKEN=${API_TOKEN}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-workflows:/home/<USER>/workflows
    depends_on:
      - postgres-n8n
    networks:
      - agenteia-network

  # PostgreSQL para N8N
  postgres-n8n:
    image: postgres:15-alpine
    container_name: agenteia-postgres-n8n
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n_password
    volumes:
      - postgres_n8n_data:/var/lib/postgresql/data
    networks:
      - agenteia-network

volumes:
  n8n_data:
  postgres_n8n_data:
```

### 2. Configurar Variáveis de Ambiente

Crie/edite o arquivo `.env`:

```env
# OpenAI
OPENAI_API_KEY=sk-sua-chave-openai-aqui

# WhatsApp Business API
WHATSAPP_TOKEN=seu_token_whatsapp_business
WHATSAPP_PHONE_ID=seu_phone_number_id

# Sistema API
API_TOKEN=seu_jwt_token_do_sistema

# Slack (opcional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=sua_senha_de_app
```

### 3. Inicializar Containers

```bash
# Iniciar N8N e dependências
docker-compose up -d n8n postgres-n8n

# Verificar logs
docker-compose logs -f n8n
```

### 4. Acessar N8N

- **URL**: http://localhost:5678
- **Usuário**: admin
- **Senha**: agenteia123

## 📋 Configuração Inicial

### 1. Configurar Credenciais

No N8N, vá em **Settings > Credentials** e configure:

#### OpenAI
- **Name**: OpenAI Agenteia
- **API Key**: Sua chave da OpenAI

#### HTTP Header Auth (para API do Sistema)
- **Name**: Sistema API Auth
- **Header Name**: Authorization
- **Header Value**: Bearer SEU_JWT_TOKEN

#### Gmail
- **Name**: Gmail Agenteia
- **Email**: <EMAIL>
- **Password**: sua_senha_de_app

#### PostgreSQL (Sistema Principal)
- **Name**: PostgreSQL Agenteia
- **Host**: postgres (nome do container)
- **Port**: 5432
- **Database**: agenteia_medicos
- **User**: postgres
- **Password**: postgres123

### 2. Importar Workflows

1. Acesse **Workflows** no N8N
2. Clique em **Import from file**
3. Importe os arquivos da pasta `n8n-workflows/`:
   - `agendamento-inteligente.json`
   - `triagem-whatsapp.json`
   - `lembretes-automaticos.json`
   - `analise-feedback.json`

### 3. Ativar Workflows

Para cada workflow importado:
1. Abra o workflow
2. Clique em **Active** (toggle no canto superior direito)
3. Verifique se não há erros de configuração

## 🔧 Configuração WhatsApp Business

### 1. Criar Aplicação Facebook

1. Acesse [Facebook Developers](https://developers.facebook.com)
2. Crie uma nova aplicação
3. Adicione o produto **WhatsApp Business**

### 2. Configurar Webhook

No painel do WhatsApp Business:

1. **Webhook URL**: `http://seu-dominio.com:5678/webhook/whatsapp-webhook`
2. **Verify Token**: `agenteia_verify_token`
3. **Webhook Fields**: `messages`

### 3. Obter Tokens

- **Access Token**: Copie o token temporário
- **Phone Number ID**: Copie o ID do número de telefone

### 4. Configurar Webhook no N8N

No workflow "Triagem WhatsApp":
1. Abra o node "Webhook WhatsApp"
2. Configure o path: `whatsapp-webhook`
3. Salve e ative o workflow

## 🧪 Testar Workflows

### 1. Teste de Agendamento

```bash
curl -X POST http://localhost:5678/webhook/agendar-consulta \
  -H "Content-Type: application/json" \
  -d '{
    "nome": "João Silva",
    "telefone": "***********",
    "idade": 35,
    "sintomas": "Dor de cabeça constante há 3 dias",
    "historico": "Hipertensão controlada"
  }'
```

### 2. Teste de Feedback

```bash
curl -X POST http://localhost:5678/webhook/feedback \
  -H "Content-Type: application/json" \
  -d '{
    "consulta_id": "123",
    "paciente_nome": "Maria Santos",
    "medico_nome": "Dr. João Silva",
    "especialidade": "Cardiologia",
    "data_consulta": "2025-01-30",
    "nota": 5,
    "comentario": "Excelente atendimento, médico muito atencioso",
    "recomendaria": true,
    "telefone": "11888888888"
  }'
```

### 3. Teste WhatsApp (Simulação)

```bash
curl -X POST http://localhost:5678/webhook/whatsapp-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "entry": [{
      "changes": [{
        "value": {
          "messages": [{
            "from": "55***********",
            "id": "msg123",
            "timestamp": "1640995200",
            "text": {
              "body": "Olá, estou com dor no peito"
            }
          }],
          "metadata": {
            "phone_number_id": "123456789"
          }
        }
      }]
    }]
  }'
```

## 📊 Monitoramento

### 1. Logs de Execução

No N8N:
- **Executions**: Ver histórico de execuções
- **Logs**: Verificar erros e sucessos

### 2. Métricas do Sistema

Execute no banco de dados:

```sql
-- Execuções por workflow (últimos 7 dias)
SELECT 
    workflow_name,
    COUNT(*) as total_execucoes,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as sucessos,
    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as erros
FROM workflow_logs 
WHERE started_at >= NOW() - INTERVAL 7 DAY
GROUP BY workflow_name;

-- Lembretes enviados hoje
SELECT 
    tipo,
    canal,
    COUNT(*) as quantidade
FROM lembretes_enviados 
WHERE DATE(enviado_em) = CURRENT_DATE
GROUP BY tipo, canal;

-- Análises de feedback (última semana)
SELECT 
    sentimento,
    COUNT(*) as quantidade,
    AVG(score) as score_medio
FROM feedback_analises 
WHERE analisado_em >= NOW() - INTERVAL 7 DAY
GROUP BY sentimento;
```

## 🔒 Segurança

### 1. Configurações de Produção

```yaml
environment:
  # Desabilitar auth básico em produção
  - N8N_BASIC_AUTH_ACTIVE=false
  
  # Configurar domínio real
  - N8N_HOST=n8n.agenteia.com
  - N8N_PROTOCOL=https
  - WEBHOOK_URL=https://n8n.agenteia.com
  
  # Configurar SSL
  - N8N_SSL_KEY=/certs/key.pem
  - N8N_SSL_CERT=/certs/cert.pem
```

### 2. Backup

```bash
# Backup dos workflows
docker exec agenteia-n8n n8n export:workflow --all --output=/tmp/workflows-backup.json

# Backup do banco N8N
docker exec agenteia-postgres-n8n pg_dump -U n8n n8n > n8n-backup.sql
```

## 🚨 Troubleshooting

### Problemas Comuns

1. **Webhook não recebe dados**
   - Verificar URL pública
   - Confirmar configuração no Facebook
   - Testar com ngrok para desenvolvimento

2. **OpenAI retorna erro**
   - Verificar saldo da conta
   - Confirmar API key válida
   - Verificar rate limits

3. **Banco de dados não conecta**
   - Verificar credenciais
   - Confirmar rede Docker
   - Testar conexão manual

### Logs Úteis

```bash
# Logs do N8N
docker-compose logs -f n8n

# Logs específicos de erro
docker-compose logs n8n | grep ERROR

# Status dos containers
docker-compose ps
```

---

**🎉 N8N configurado com sucesso!**

Agora você tem um sistema completo de automação com IA integrado ao seu sistema médico.
