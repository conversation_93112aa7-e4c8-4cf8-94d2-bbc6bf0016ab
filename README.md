# Sistema de Agência Médica

## Descrição

Sistema completo para gerenciamento de agência médica, incluindo cadastro de médicos, pacientes, agendamentos e histórico médico.

## Funcionalidades Principais

### 👨‍⚕️ Gestão de Médicos

- Cadastro completo de médicos
- Especialidades e horários de atendimento
- Documentos e certificações
- Agenda pessoal

### 👥 Gestão de Pacientes

- Cadastro de pacientes
- Histórico médico
- Documentos e exames
- Planos de saúde

### 📅 Sistema de Agendamentos

- Agendamento de consultas
- Calendário integrado
- Notificações automáticas
- Controle de disponibilidade

### 📊 Relatórios e Dashboard

- Relatórios de atendimentos
- Estatísticas de consultas
- Dashboard administrativo
- Exportação de dados

## Stack Tecnológico

### Frontend

- **Next.js 14** - Framework React
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Estilização
- **Shadcn/ui** - Componentes UI

### Backend

- **Node.js** - Runtime JavaScript
- **Express.js** - Framework web
- **TypeScript** - Tipagem estática
- **Prisma** - ORM

### Banco de Dados

- **PostgreSQL** - Banco principal
- **Supabase** - Backend as a Service

### Autenticação

- **Supabase Auth** - Sistema de autenticação
- **JWT** - Tokens de acesso

### 🤖 Automação e IA

- **N8N** - Workflow automation e orquestração
- **OpenAI GPT-4** - Inteligência artificial para triagem e análises
- **WhatsApp Business API** - Comunicação automatizada
- **Análise de Sentimentos** - Processamento de feedback
- **Agendamento Inteligente** - IA para sugestão de especialidades
- **Lembretes Automáticos** - Notificações personalizadas

### 🐳 Infraestrutura

- **Docker + Docker Compose** - Containerização
- **PostgreSQL** - Banco de dados principal
- **Redis** - Cache em memória
- **Adminer** - Interface de administração do banco

## Estrutura do Projeto

```
agenteia-medicos/
├── frontend/          # Aplicação Next.js
├── backend/           # API Node.js + Controllers
├── shared/            # Tipos e utilitários compartilhados
├── n8n-workflows/     # Workflows de automação com IA
├── docs/              # Documentação completa
├── database/          # Scripts e migrações
├── docker-compose.yml # Configuração Docker
└── README.md          # Este arquivo
```

## Perfis de Usuário

### 🔐 Administrador

- Acesso completo ao sistema
- Gestão de usuários
- Relatórios gerenciais

### 👨‍⚕️ Médico

- Gestão da própria agenda
- Acesso aos pacientes
- Histórico de consultas

### 👤 Recepcionista

- Agendamento de consultas
- Cadastro de pacientes
- Gestão da agenda geral

### 🏥 Paciente

- Visualização de consultas
- Histórico médico pessoal
- Agendamento online

## 🤖 Workflows de IA Implementados

### 📱 Triagem Automática via WhatsApp

- **Entrada**: Mensagens de pacientes via WhatsApp
- **IA**: Análise de sintomas e classificação de urgência
- **Saída**: Resposta personalizada + agendamento automático se necessário

### 🎯 Agendamento Inteligente

- **Entrada**: Dados do paciente + sintomas
- **IA**: Sugestão de especialidade + médico + horário
- **Saída**: Consulta agendada + confirmação via WhatsApp

### ⏰ Lembretes Automáticos

- **Trigger**: Cron job (a cada 2 horas)
- **IA**: Personalização de mensagens de lembrete
- **Saída**: WhatsApp + Email 24h e 1h antes da consulta

### 📊 Análise de Feedback

- **Entrada**: Feedback pós-consulta
- **IA**: Análise de sentimentos + categorização
- **Saída**: Alertas para gestão + agradecimentos automáticos

### 🔗 Integrações Disponíveis

- **WhatsApp Business API** - Comunicação bidirecional
- **OpenAI GPT-4** - Processamento de linguagem natural
- **Google Calendar** - Sincronização de agenda
- **Slack/Teams** - Notificações para equipe
- **Email SMTP** - Envio de relatórios e lembretes

## Instalação e Configuração

### Pré-requisitos

- Node.js 18+
- PostgreSQL 14+
- npm ou yarn

### Configuração Inicial

```bash
# Clonar o repositório
git clone <repository-url>
cd agenteia-medicos

# Instalar dependências
npm install

# Configurar variáveis de ambiente
cp .env.example .env.local

# Executar migrações
npm run db:migrate

# Iniciar desenvolvimento
npm run dev
```

### 🤖 Configuração dos Workflows de IA

#### 1. Inicializar N8N com Docker

```bash
# Iniciar todos os serviços incluindo N8N
docker-compose up -d

# Acessar N8N
# URL: http://localhost:5678
# Usuário: admin
# Senha: agenteia123
```

#### 2. Configurar Credenciais

No painel do N8N, configure:

- **OpenAI**: Sua chave da API OpenAI
- **WhatsApp Business**: Token e Phone ID
- **PostgreSQL**: Conexão com o banco principal
- **Gmail**: Para envio de emails

#### 3. Importar Workflows

```bash
# Os workflows estão em n8n-workflows/
# Importe via interface do N8N:
# - agendamento-inteligente.json
# - triagem-whatsapp.json
# - lembretes-automaticos.json
# - analise-feedback.json
```

#### 4. Testar Integrações

```bash
# Teste de agendamento via webhook
curl -X POST http://localhost:5678/webhook/agendar-consulta \
  -H "Content-Type: application/json" \
  -d '{"nome":"João","telefone":"***********","sintomas":"dor de cabeça"}'

# Teste de feedback
curl -X POST http://localhost:5678/webhook/feedback \
  -H "Content-Type: application/json" \
  -d '{"paciente_nome":"Maria","nota":5,"comentario":"Ótimo atendimento"}'
```

### 📚 Documentação Adicional

- **[Configuração N8N Completa](docs/n8n-instalacao.md)** - Guia detalhado
- **[Setup de Workflows](docs/n8n-workflow-setup.md)** - Documentação técnica
- **[Instalação Docker](docs/docker-setup.md)** - Ambiente completo

## Licença

MIT License

## Contribuição

Contribuições são bem-vindas! Por favor, leia as diretrizes de contribuição antes de submeter um PR.

---

**Desenvolvido com ❤️ para facilitar o atendimento médico**
