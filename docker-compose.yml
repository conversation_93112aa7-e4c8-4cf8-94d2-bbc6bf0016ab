services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: agenteia-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: agenteia_medicos
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - agenteia-network

  # Redis para cache (opcional)
  redis:
    image: redis:7-alpine
    container_name: agenteia-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agenteia-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile.dev
    container_name: agenteia-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/agenteia_medicos
      JWT_SECRET: desenvolvimento_jwt_secret_muito_seguro
      PORT: 3001
      APP_URL: http://localhost:3000
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - agenteia-network
    command: npm run dev

  # Frontend Next.js
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: agenteia-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - agenteia-network
    command: npm run dev

  # Adminer para gerenciar banco de dados
  adminer:
    image: adminer:latest
    container_name: agenteia-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - agenteia-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  agenteia-network:
    driver: bridge
