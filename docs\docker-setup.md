# Guia de Configuração com Docker - Sistema de Agência Médica

## 🐳 Pré-requisitos

### Software Necessário
- **Docker Desktop** ([Download](https://www.docker.com/products/docker-desktop/))
- **Docker Compose** (incluído no Docker Desktop)
- **Git** ([Download](https://git-scm.com/))

### Verificar Instalação
```bash
docker --version
docker-compose --version
```

## 🚀 Inicialização Rápida

### 1. Clonar o Repositório
```bash
git clone <url-do-repositorio>
cd agenteia-medicos
```

### 2. Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar variáveis (opcional para desenvolvimento)
# As configurações padrão do Docker já funcionam
```

### 3. Inicializar com Docker
```bash
# Construir e iniciar todos os serviços
docker-compose up --build

# Ou em background
docker-compose up -d --build
```

### 4. Executar Migrações e Seed
```bash
# Aguardar os containers iniciarem (30-60 segundos)
# Então executar as migrações
docker-compose exec backend npm run db:migrate

# Executar seed para dados iniciais
docker-compose exec backend npm run db:seed
```

### 5. Acessar a Aplicação
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Adminer (DB Admin)**: http://localhost:8080
- **Health Check**: http://localhost:3001/api/health

## 📋 Serviços Incluídos

### 🗄️ PostgreSQL (porta 5432)
- **Database**: agenteia_medicos
- **Usuário**: postgres
- **Senha**: postgres123
- **Host**: localhost (ou postgres dentro do Docker)

### 🔧 Backend API (porta 3001)
- Node.js + Express + TypeScript
- Prisma ORM
- Autenticação JWT
- Hot reload habilitado

### 🎨 Frontend (porta 3000)
- Next.js 14 + TypeScript
- Tailwind CSS + Shadcn/ui
- Hot reload habilitado

### 📊 Adminer (porta 8080)
- Interface web para gerenciar PostgreSQL
- **Servidor**: postgres
- **Usuário**: postgres
- **Senha**: postgres123
- **Base de dados**: agenteia_medicos

### 🚀 Redis (porta 6379)
- Cache em memória (para uso futuro)
- Configurado mas não utilizado ainda

## 🛠️ Comandos Úteis

### Gerenciamento de Containers
```bash
# Iniciar serviços
docker-compose up

# Iniciar em background
docker-compose up -d

# Parar serviços
docker-compose down

# Parar e remover volumes
docker-compose down -v

# Reconstruir containers
docker-compose up --build

# Ver logs
docker-compose logs

# Ver logs de um serviço específico
docker-compose logs backend
docker-compose logs frontend
```

### Banco de Dados
```bash
# Executar migrações
docker-compose exec backend npm run db:migrate

# Gerar cliente Prisma
docker-compose exec backend npm run db:generate

# Executar seed
docker-compose exec backend npm run db:seed

# Abrir Prisma Studio
docker-compose exec backend npm run db:studio

# Reset do banco (cuidado!)
docker-compose exec backend npx prisma migrate reset
```

### Desenvolvimento
```bash
# Acessar container do backend
docker-compose exec backend sh

# Acessar container do frontend
docker-compose exec frontend sh

# Instalar nova dependência no backend
docker-compose exec backend npm install <pacote>

# Instalar nova dependência no frontend
docker-compose exec frontend npm install <pacote>

# Executar testes
docker-compose exec backend npm test
docker-compose exec frontend npm test
```

### Logs e Debugging
```bash
# Ver logs em tempo real
docker-compose logs -f

# Ver logs do backend
docker-compose logs -f backend

# Ver status dos containers
docker-compose ps

# Ver uso de recursos
docker stats
```

## 🔧 Configurações Avançadas

### Variáveis de Ambiente
O arquivo `docker-compose.yml` já inclui as configurações necessárias para desenvolvimento:

```yaml
# Backend
NODE_ENV: development
DATABASE_URL: ***********************************************/agenteia_medicos
JWT_SECRET: desenvolvimento_jwt_secret_muito_seguro
PORT: 3001

# Frontend
NEXT_PUBLIC_API_URL: http://localhost:3001
```

### Volumes Persistentes
- **postgres_data**: Dados do PostgreSQL
- **redis_data**: Dados do Redis
- **Código fonte**: Montado como volume para hot reload

### Rede Docker
Todos os serviços estão na rede `agenteia-network` e podem se comunicar pelos nomes dos serviços.

## 🐛 Solução de Problemas

### Container não inicia
```bash
# Ver logs detalhados
docker-compose logs <nome-do-servico>

# Reconstruir container
docker-compose up --build <nome-do-servico>

# Limpar cache do Docker
docker system prune -a
```

### Erro de conexão com banco
```bash
# Verificar se PostgreSQL está rodando
docker-compose ps postgres

# Reiniciar apenas o banco
docker-compose restart postgres

# Ver logs do PostgreSQL
docker-compose logs postgres
```

### Porta em uso
```bash
# Verificar processos nas portas
netstat -ano | findstr :3000
netstat -ano | findstr :3001
netstat -ano | findstr :5432

# Parar containers e tentar novamente
docker-compose down
docker-compose up
```

### Problemas de permissão (Linux/Mac)
```bash
# Dar permissões aos scripts
chmod +x scripts/*.sh

# Executar com sudo se necessário
sudo docker-compose up
```

### Limpar tudo e recomeçar
```bash
# Parar e remover containers, redes e volumes
docker-compose down -v --remove-orphans

# Remover imagens
docker-compose down --rmi all

# Reconstruir tudo
docker-compose up --build
```

## 📊 Monitoramento

### Health Checks
```bash
# Verificar saúde da API
curl http://localhost:3001/api/health

# Verificar frontend
curl http://localhost:3000

# Verificar banco via Adminer
# Acesse http://localhost:8080
```

### Logs Estruturados
Os logs são formatados e incluem:
- Timestamp
- Nível de log
- Serviço
- Mensagem
- Contexto adicional

## 🔄 Workflow de Desenvolvimento

### 1. Inicialização Diária
```bash
# Iniciar ambiente
docker-compose up -d

# Verificar status
docker-compose ps
```

### 2. Durante o Desenvolvimento
- Código é sincronizado automaticamente (hot reload)
- Banco persiste entre reinicializações
- Logs disponíveis em tempo real

### 3. Finalização
```bash
# Parar ambiente (mantém dados)
docker-compose down

# Ou parar e limpar (remove dados)
docker-compose down -v
```

## 🚀 Deploy para Produção

### Configurações de Produção
1. Alterar `NODE_ENV` para `production`
2. Configurar variáveis de ambiente seguras
3. Usar banco de dados externo
4. Configurar SSL/HTTPS
5. Implementar backup automático

### Docker Compose para Produção
```bash
# Usar arquivo específico para produção
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 Recursos Adicionais

### Documentação
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)

### Comandos de Referência
```bash
# Backup do banco
docker-compose exec postgres pg_dump -U postgres agenteia_medicos > backup.sql

# Restaurar backup
docker-compose exec -T postgres psql -U postgres agenteia_medicos < backup.sql

# Conectar diretamente ao PostgreSQL
docker-compose exec postgres psql -U postgres -d agenteia_medicos
```

---

**🎉 Ambiente Docker configurado com sucesso!**

Agora você pode desenvolver com facilidade usando containers Docker. Todos os serviços estão configurados e prontos para uso.
