{"name": "agenteia-medicos-backend", "version": "1.0.0", "description": "Backend API para sistema de agência médica", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["medicina", "api", "nodejs", "express", "typescript", "prisma"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "prisma": "^6.13.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/helmet": "^0.0.48", "@types/morgan": "^1.9.10", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1"}}