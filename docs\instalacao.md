# Guia de Instalação - Sistema de Agência Médica

## Pré-requisitos

### Software Necessário
- **Node.js** 18+ ([Download](https://nodejs.org/))
- **PostgreSQL** 14+ ([Download](https://www.postgresql.org/download/))
- **Git** ([Download](https://git-scm.com/))
- **Editor de Código** (VS Code recomendado)

### Contas de Serviço
- **Supabase** (para autenticação e storage) - [supabase.com](https://supabase.com)
- **Gmail** (para envio de emails) - Configurar senha de app

## Instalação Passo a Passo

### 1. Clonar o Repositório
```bash
git clone <url-do-repositorio>
cd agenteia-medicos
```

### 2. Instalar Dependências
```bash
# Instalar dependências do projeto principal
npm install

# Instalar dependências do frontend
cd frontend
npm install

# Instalar dependências do backend
cd ../backend
npm install

# Instalar dependências dos tipos compartilhados
cd ../shared
npm install

# Voltar ao diretório raiz
cd ..
```

### 3. Configurar Banco de Dados

#### Opção A: PostgreSQL Local
```bash
# Criar banco de dados
createdb agenteia_medicos

# Ou via psql
psql -U postgres
CREATE DATABASE agenteia_medicos;
\q
```

#### Opção B: Supabase (Recomendado)
1. Acesse [supabase.com](https://supabase.com)
2. Crie uma nova conta/projeto
3. Anote a URL e as chaves do projeto

### 4. Configurar Variáveis de Ambiente

#### Backend (.env)
```bash
cd backend
cp ../.env.example .env
```

Edite o arquivo `.env` com suas configurações:
```env
# Banco de Dados
DATABASE_URL="postgresql://usuario:senha@localhost:5432/agenteia_medicos"
# OU para Supabase:
# DATABASE_URL="postgresql://postgres:[SUA-SENHA]@db.[SEU-PROJETO].supabase.co:5432/postgres"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="https://[SEU-PROJETO].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[SUA-CHAVE-ANONIMA]"
SUPABASE_SERVICE_ROLE_KEY="[SUA-CHAVE-DE-SERVICO]"

# JWT
JWT_SECRET="seu_jwt_secret_muito_seguro_aqui"
JWT_EXPIRES_IN="7d"

# Servidor
PORT=3001
NODE_ENV="development"

# Email (Gmail)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="sua_senha_de_app_do_gmail"

# Aplicação
APP_NAME="Agenteia Médicos"
APP_URL="http://localhost:3000"
API_URL="http://localhost:3001"
```

#### Frontend (.env.local)
```bash
cd ../frontend
cp ../.env.example .env.local
```

Edite o arquivo `.env.local`:
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL="https://[SEU-PROJETO].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[SUA-CHAVE-ANONIMA]"

# API
NEXT_PUBLIC_API_URL="http://localhost:3001"
```

### 5. Configurar Banco de Dados com Prisma

```bash
cd backend

# Gerar cliente Prisma
npm run db:generate

# Executar migrações
npm run db:migrate

# (Opcional) Executar seed para dados iniciais
npm run db:seed
```

### 6. Compilar Tipos Compartilhados

```bash
cd ../shared
npm run build
```

## Executar o Projeto

### Desenvolvimento (Recomendado)
```bash
# No diretório raiz
npm run dev
```

Isso iniciará:
- Frontend em: http://localhost:3000
- Backend em: http://localhost:3001

### Executar Separadamente

#### Frontend
```bash
cd frontend
npm run dev
```

#### Backend
```bash
cd backend
npm run dev
```

## Verificar Instalação

### 1. Testar Backend
```bash
curl http://localhost:3001/api/health
```

Deve retornar:
```json
{
  "status": "ok",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

### 2. Testar Frontend
Acesse http://localhost:3000 no navegador

### 3. Testar Banco de Dados
```bash
cd backend
npm run db:studio
```

Isso abrirá o Prisma Studio em http://localhost:5555

## Configurações Adicionais

### Configurar Gmail para Envio de Emails

1. Acesse [myaccount.google.com](https://myaccount.google.com)
2. Vá em "Segurança" → "Verificação em duas etapas"
3. Ative a verificação em duas etapas
4. Vá em "Senhas de app"
5. Gere uma senha para "Email"
6. Use essa senha no arquivo `.env`

### Configurar Supabase Auth

1. No painel do Supabase, vá em "Authentication"
2. Configure os provedores desejados
3. Ajuste as configurações de email
4. Configure as políticas RLS se necessário

### Configurar Storage (Supabase)

1. No painel do Supabase, vá em "Storage"
2. Crie um bucket chamado "uploads"
3. Configure as políticas de acesso

## Solução de Problemas

### Erro de Conexão com Banco
- Verifique se o PostgreSQL está rodando
- Confirme as credenciais no `.env`
- Teste a conexão: `psql -U usuario -d agenteia_medicos`

### Erro de Migração Prisma
```bash
cd backend
npx prisma migrate reset
npm run db:migrate
```

### Erro de Dependências
```bash
# Limpar cache e reinstalar
rm -rf node_modules package-lock.json
npm install
```

### Porta em Uso
```bash
# Verificar processos na porta
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# Matar processo (Windows)
taskkill /PID [PID] /F
```

## Scripts Úteis

```bash
# Desenvolvimento
npm run dev              # Iniciar frontend e backend
npm run dev:frontend     # Apenas frontend
npm run dev:backend      # Apenas backend

# Build
npm run build           # Build completo
npm run build:frontend  # Build frontend
npm run build:backend   # Build backend

# Banco de dados
npm run db:migrate      # Executar migrações
npm run db:generate     # Gerar cliente Prisma
npm run db:studio       # Abrir Prisma Studio
npm run db:seed         # Executar seed

# Testes
npm run test           # Executar todos os testes
npm run test:frontend  # Testes do frontend
npm run test:backend   # Testes do backend

# Linting
npm run lint           # Lint completo
npm run lint:frontend  # Lint frontend
npm run lint:backend   # Lint backend
```

## Próximos Passos

Após a instalação bem-sucedida:

1. **Configurar dados iniciais** - Execute o seed para criar especialidades e usuário admin
2. **Personalizar configurações** - Ajuste as configurações no painel admin
3. **Configurar notificações** - Teste o envio de emails
4. **Criar primeiro médico** - Cadastre um médico para testar o sistema
5. **Configurar backup** - Configure rotinas de backup do banco

## Suporte

Para dúvidas ou problemas:
- Consulte a documentação em `/docs`
- Verifique os logs do sistema
- Abra uma issue no repositório
