# Workflow N8N com IA - Sistema de Agência Médica

## 🤖 Visão Geral

Este documento descreve a implementação de workflows automatizados usando N8N integrado com IA para o sistema de agência médica, incluindo:

- Agendamento inteligente de consultas
- Triagem automática de pacientes
- Notificações personalizadas
- Análise de sentimentos
- Relatórios automáticos
- Integração com WhatsApp/Telegram

## 🏗️ Arquitetura do Workflow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Webhook       │    │   IA Agent      │    │   Database      │
│   (Entrada)     │───►│   (OpenAI)      │───►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp      │    │   Email         │    │   SMS           │
│   (Notificação) │    │   (Relatórios)  │    │   (Lembretes)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Configuração Inicial

### 1. Instalação do N8N

```bash
# Via Docker
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n

# Via NPM
npm install n8n -g
n8n start
```

### 2. Variáveis de Ambiente

```env
# N8N Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http

# Database Connection
DB_TYPE=postgresdb
DB_HOST=localhost
DB_PORT=5433
DB_DATABASE=agenteia_medicos
DB_USERNAME=postgres
DB_PASSWORD=postgres123

# OpenAI API
OPENAI_API_KEY=sua_chave_openai

# WhatsApp Business API
WHATSAPP_TOKEN=seu_token_whatsapp
WHATSAPP_PHONE_ID=seu_phone_id

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=sua_senha_app

# Sistema API
API_BASE_URL=http://localhost:3001/api
API_TOKEN=token_do_sistema
```

## 🤖 Workflows Implementados

### 1. Agendamento Inteligente de Consultas

**Trigger**: Webhook POST `/webhook/agendar-consulta`

**Fluxo**:
1. Recebe dados do paciente via webhook
2. IA analisa a descrição dos sintomas
3. Sugere especialidade mais adequada
4. Verifica disponibilidade de médicos
5. Agenda automaticamente
6. Envia confirmação via WhatsApp

### 2. Triagem Automática de Pacientes

**Trigger**: Webhook POST `/webhook/triagem`

**Fluxo**:
1. Paciente descreve sintomas via chat
2. IA faz triagem inicial
3. Classifica urgência (baixa, média, alta, emergência)
4. Direciona para especialista adequado
5. Agenda consulta prioritária se necessário

### 3. Lembretes Inteligentes

**Trigger**: Cron job (executa a cada hora)

**Fluxo**:
1. Busca consultas nas próximas 24h
2. IA personaliza mensagem de lembrete
3. Envia via WhatsApp, SMS ou email
4. Registra confirmação de recebimento

### 4. Análise de Feedback

**Trigger**: Webhook POST `/webhook/feedback`

**Fluxo**:
1. Recebe feedback pós-consulta
2. IA analisa sentimento (positivo/negativo)
3. Extrai insights e sugestões
4. Gera relatório para gestão
5. Aciona follow-up se necessário

## 📱 Integração WhatsApp Business

### Configuração do Webhook

```javascript
// Webhook para receber mensagens do WhatsApp
{
  "httpMethod": "POST",
  "path": "/webhook/whatsapp",
  "responseMode": "responseNode",
  "options": {}
}
```

### Processamento de Mensagens

```javascript
// Node de processamento IA
const message = $json.entry[0].changes[0].value.messages[0];
const userPhone = message.from;
const messageText = message.text.body;

// Enviar para OpenAI para processamento
const aiResponse = await openai.chat.completions.create({
  model: "gpt-4",
  messages: [
    {
      role: "system",
      content: `Você é um assistente médico virtual da Agenteia Médicos. 
      Ajude o paciente com agendamentos, informações e triagem inicial.
      Seja empático, profissional e sempre recomende consulta médica para diagnósticos.`
    },
    {
      role: "user",
      content: messageText
    }
  ]
});

return {
  phone: userPhone,
  response: aiResponse.choices[0].message.content
};
```

## 🧠 Prompts de IA Especializados

### 1. Triagem Médica

```
Você é um assistente de triagem médica. Analise os sintomas descritos e:

1. Classifique a urgência (1-4):
   - 1: Baixa (consulta de rotina)
   - 2: Média (consulta em 1-2 semanas)
   - 3: Alta (consulta em 1-3 dias)
   - 4: Emergência (atendimento imediato)

2. Sugira a especialidade mais adequada
3. Faça perguntas de esclarecimento se necessário
4. SEMPRE recomende consulta médica presencial

Sintomas: {sintomas}
```

### 2. Agendamento Inteligente

```
Baseado nos sintomas e preferências do paciente, sugira:

1. Especialidade médica mais adequada
2. Tipo de consulta (primeira consulta, retorno, urgente)
3. Horário preferencial baseado no perfil
4. Preparação necessária para a consulta

Dados do paciente:
- Sintomas: {sintomas}
- Idade: {idade}
- Histórico: {historico}
- Preferências: {preferencias}
```

### 3. Personalização de Mensagens

```
Crie uma mensagem personalizada de lembrete de consulta:

1. Use tom amigável e profissional
2. Inclua informações relevantes
3. Adicione instruções de preparação se necessário
4. Mantenha conciso (máximo 160 caracteres para SMS)

Dados da consulta:
- Paciente: {nome}
- Médico: {medico}
- Data/Hora: {dataHora}
- Especialidade: {especialidade}
- Tipo: {tipo}
```

## 📊 Workflows de Relatórios

### 1. Relatório Diário Automatizado

**Trigger**: Cron job (todo dia às 8h)

**Fluxo**:
1. Coleta dados do dia anterior
2. IA gera insights e análises
3. Cria relatório em PDF
4. Envia por email para gestores

### 2. Análise de Performance

**Trigger**: Cron job (toda segunda-feira)

**Fluxo**:
1. Analisa métricas da semana
2. IA identifica tendências e padrões
3. Sugere melhorias operacionais
4. Gera dashboard interativo

## 🔗 Integrações Externas

### 1. Google Calendar

```javascript
// Sincronizar consultas com Google Calendar
{
  "summary": "Consulta - {{$json.paciente.nome}}",
  "description": "Consulta de {{$json.especialidade}} com Dr. {{$json.medico.nome}}",
  "start": {
    "dateTime": "{{$json.dataHora}}",
    "timeZone": "America/Sao_Paulo"
  },
  "end": {
    "dateTime": "{{$json.dataHoraFim}}",
    "timeZone": "America/Sao_Paulo"
  }
}
```

### 2. Slack/Teams

```javascript
// Notificações para equipe médica
{
  "text": "🏥 Nova consulta urgente agendada",
  "attachments": [
    {
      "color": "danger",
      "fields": [
        {
          "title": "Paciente",
          "value": "{{$json.paciente.nome}}",
          "short": true
        },
        {
          "title": "Médico",
          "value": "Dr. {{$json.medico.nome}}",
          "short": true
        },
        {
          "title": "Data/Hora",
          "value": "{{$json.dataHora}}",
          "short": true
        }
      ]
    }
  ]
}
```

## 📋 Monitoramento e Logs

### 1. Webhook de Monitoramento

```javascript
// Registrar todas as execuções
{
  "workflow": "{{$workflow.name}}",
  "execution_id": "{{$execution.id}}",
  "status": "{{$execution.status}}",
  "timestamp": "{{$now}}",
  "data": "{{$json}}"
}
```

### 2. Alertas de Erro

```javascript
// Notificar erros críticos
if ($execution.status === 'error') {
  // Enviar alerta para administradores
  return {
    "alert": "Erro no workflow",
    "workflow": $workflow.name,
    "error": $execution.error,
    "timestamp": new Date().toISOString()
  };
}
```

## 🚀 Deploy e Configuração

### 1. Docker Compose

```yaml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres-n8n

  postgres-n8n:
    image: postgres:13
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n
    volumes:
      - postgres_n8n_data:/var/lib/postgresql/data

volumes:
  n8n_data:
  postgres_n8n_data:
```

### 2. Configuração de Credenciais

1. **OpenAI**: Adicionar API key nas credenciais
2. **WhatsApp**: Configurar webhook e token
3. **Database**: Conectar ao PostgreSQL do sistema
4. **Email**: Configurar SMTP

## 📈 Métricas e KPIs

### Dashboards Automáticos

1. **Taxa de Agendamentos**: Conversão de contatos em consultas
2. **Satisfação do Cliente**: Análise de sentimentos
3. **Eficiência Operacional**: Tempo de resposta e resolução
4. **Predições**: Demanda futura baseada em IA

---

**🎯 Próximos Passos:**
1. Importar workflows no N8N
2. Configurar credenciais
3. Testar integrações
4. Monitorar performance
