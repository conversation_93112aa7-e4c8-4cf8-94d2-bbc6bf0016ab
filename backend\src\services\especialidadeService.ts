import { PrismaClient } from '@prisma/client'
import { CreateEspecialidadeDTO, PaginationParams } from '../../../shared/src/types'

const prisma = new PrismaClient()

export class EspecialidadeService {
  /**
   * Criar nova especialidade
   */
  async create(data: CreateEspecialidadeDTO) {
    const { nome, descricao, valorConsulta } = data

    // Verificar se especialidade já existe
    const especialidadeExistente = await prisma.especialidade.findUnique({
      where: { nome }
    })

    if (especialidadeExistente) {
      throw new Error('Especialidade já existe')
    }

    if (valorConsulta <= 0) {
      throw new Error('Valor da consulta deve ser maior que zero')
    }

    const especialidade = await prisma.especialidade.create({
      data: {
        nome,
        descricao,
        valorConsulta
      },
      include: {
        _count: {
          select: {
            medicos: true,
            consultas: true
          }
        }
      }
    })

    return especialidade
  }

  /**
   * Buscar especialidade por ID
   */
  async findById(id: string) {
    const especialidade = await prisma.especialidade.findUnique({
      where: { id },
      include: {
        medicos: {
          include: {
            medico: {
              include: {
                usuario: {
                  select: {
                    id: true,
                    nome: true,
                    telefone: true,
                    status: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            medicos: true,
            consultas: true
          }
        }
      }
    })

    if (!especialidade) {
      throw new Error('Especialidade não encontrada')
    }

    return especialidade
  }

  /**
   * Listar especialidades com paginação
   */
  async findMany(params: PaginationParams = {}) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'nome',
      sortOrder = 'asc'
    } = params

    const skip = (page - 1) * limit

    const [especialidades, total] = await Promise.all([
      prisma.especialidade.findMany({
        include: {
          _count: {
            select: {
              medicos: true,
              consultas: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        },
        skip,
        take: limit
      }),
      prisma.especialidade.count()
    ])

    return {
      data: especialidades,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * Listar todas as especialidades (sem paginação)
   */
  async findAll() {
    return prisma.especialidade.findMany({
      include: {
        _count: {
          select: {
            medicos: true,
            consultas: true
          }
        }
      },
      orderBy: {
        nome: 'asc'
      }
    })
  }

  /**
   * Atualizar especialidade
   */
  async update(id: string, data: Partial<CreateEspecialidadeDTO>) {
    const { nome, descricao, valorConsulta } = data

    // Verificar se especialidade existe
    await this.findById(id)

    // Se nome foi alterado, verificar se não existe outra com o mesmo nome
    if (nome) {
      const especialidadeExistente = await prisma.especialidade.findFirst({
        where: {
          nome,
          id: { not: id }
        }
      })

      if (especialidadeExistente) {
        throw new Error('Já existe uma especialidade com este nome')
      }
    }

    if (valorConsulta !== undefined && valorConsulta <= 0) {
      throw new Error('Valor da consulta deve ser maior que zero')
    }

    const especialidade = await prisma.especialidade.update({
      where: { id },
      data: {
        ...(nome && { nome }),
        ...(descricao !== undefined && { descricao }),
        ...(valorConsulta !== undefined && { valorConsulta })
      },
      include: {
        _count: {
          select: {
            medicos: true,
            consultas: true
          }
        }
      }
    })

    return especialidade
  }

  /**
   * Deletar especialidade
   */
  async delete(id: string) {
    // Verificar se especialidade existe
    await this.findById(id)

    // Verificar se há médicos associados
    const medicosAssociados = await prisma.medicoEspecialidade.count({
      where: { especialidadeId: id }
    })

    if (medicosAssociados > 0) {
      throw new Error('Não é possível deletar especialidade com médicos associados')
    }

    // Verificar se há consultas associadas
    const consultasAssociadas = await prisma.consulta.count({
      where: { especialidadeId: id }
    })

    if (consultasAssociadas > 0) {
      throw new Error('Não é possível deletar especialidade com consultas associadas')
    }

    await prisma.especialidade.delete({
      where: { id }
    })

    return { message: 'Especialidade deletada com sucesso' }
  }

  /**
   * Buscar especialidades com médicos disponíveis
   */
  async findWithMedicos() {
    return prisma.especialidade.findMany({
      where: {
        medicos: {
          some: {
            medico: {
              usuario: {
                status: 'ATIVO'
              }
            }
          }
        }
      },
      include: {
        medicos: {
          where: {
            medico: {
              usuario: {
                status: 'ATIVO'
              }
            }
          },
          include: {
            medico: {
              include: {
                usuario: {
                  select: {
                    id: true,
                    nome: true,
                    telefone: true
                  }
                },
                horarios: {
                  where: { ativo: true }
                }
              }
            }
          }
        },
        _count: {
          select: {
            medicos: true,
            consultas: true
          }
        }
      },
      orderBy: {
        nome: 'asc'
      }
    })
  }

  /**
   * Obter estatísticas da especialidade
   */
  async getStats(id: string) {
    // Verificar se especialidade existe
    await this.findById(id)

    const [
      totalMedicos,
      totalConsultas,
      consultasHoje,
      consultasSemana,
      consultasMes,
      receitaMes
    ] = await Promise.all([
      // Total de médicos
      prisma.medicoEspecialidade.count({
        where: { especialidadeId: id }
      }),
      // Total de consultas
      prisma.consulta.count({
        where: { especialidadeId: id }
      }),
      // Consultas hoje
      prisma.consulta.count({
        where: {
          especialidadeId: id,
          dataHora: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lt: new Date(new Date().setHours(23, 59, 59, 999))
          }
        }
      }),
      // Consultas esta semana
      prisma.consulta.count({
        where: {
          especialidadeId: id,
          dataHora: {
            gte: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())),
            lt: new Date()
          }
        }
      }),
      // Consultas este mês
      prisma.consulta.count({
        where: {
          especialidadeId: id,
          dataHora: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            lt: new Date()
          }
        }
      }),
      // Receita do mês
      prisma.consulta.aggregate({
        where: {
          especialidadeId: id,
          dataHora: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            lt: new Date()
          },
          valor: { not: null }
        },
        _sum: {
          valor: true
        }
      })
    ])

    return {
      totalMedicos,
      totalConsultas,
      consultasHoje,
      consultasSemana,
      consultasMes,
      receitaMes: receitaMes._sum.valor || 0
    }
  }

  /**
   * Buscar especialidades mais populares
   */
  async findMostPopular(limit: number = 5) {
    const especialidades = await prisma.especialidade.findMany({
      include: {
        _count: {
          select: {
            consultas: true
          }
        }
      },
      orderBy: {
        consultas: {
          _count: 'desc'
        }
      },
      take: limit
    })

    return especialidades
  }
}
