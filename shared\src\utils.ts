// Utilitários compartilhados entre frontend e backend

import { TipoUsuario, StatusConsulta, StatusUsuario } from './types'

// Validações
export const validarEmail = (email: string): boolean => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return regex.test(email)
}

export const validarCPF = (cpf: string): boolean => {
  // Remove caracteres não numéricos
  const cpfLimpo = cpf.replace(/\D/g, '')
  
  // Verifica se tem 11 dígitos
  if (cpfLimpo.length !== 11) return false
  
  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cpfLimpo)) return false
  
  // Validação do primeiro dígito verificador
  let soma = 0
  for (let i = 0; i < 9; i++) {
    soma += parseInt(cpfLimpo.charAt(i)) * (10 - i)
  }
  let resto = 11 - (soma % 11)
  if (resto === 10 || resto === 11) resto = 0
  if (resto !== parseInt(cpfLimpo.charAt(9))) return false
  
  // Validação do segundo dígito verificador
  soma = 0
  for (let i = 0; i < 10; i++) {
    soma += parseInt(cpfLimpo.charAt(i)) * (11 - i)
  }
  resto = 11 - (soma % 11)
  if (resto === 10 || resto === 11) resto = 0
  if (resto !== parseInt(cpfLimpo.charAt(10))) return false
  
  return true
}

export const validarCRM = (crm: string): boolean => {
  // CRM deve ter formato: 123456/UF
  const regex = /^\d{4,6}\/[A-Z]{2}$/
  return regex.test(crm)
}

export const validarTelefone = (telefone: string): boolean => {
  // Remove caracteres não numéricos
  const telefoneLimpo = telefone.replace(/\D/g, '')
  
  // Verifica se tem 10 ou 11 dígitos (com ou sem 9 no celular)
  return telefoneLimpo.length === 10 || telefoneLimpo.length === 11
}

// Formatadores
export const formatarCPF = (cpf: string): string => {
  const cpfLimpo = cpf.replace(/\D/g, '')
  return cpfLimpo.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
}

export const formatarTelefone = (telefone: string): string => {
  const telefoneLimpo = telefone.replace(/\D/g, '')
  
  if (telefoneLimpo.length === 10) {
    return telefoneLimpo.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3')
  } else if (telefoneLimpo.length === 11) {
    return telefoneLimpo.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3')
  }
  
  return telefone
}

export const formatarMoeda = (valor: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(valor)
}

export const formatarData = (data: Date): string => {
  return new Intl.DateTimeFormat('pt-BR').format(data)
}

export const formatarDataHora = (data: Date): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(data)
}

// Utilitários de data
export const obterDiaSemana = (data: Date): number => {
  return data.getDay() // 0 = Domingo, 1 = Segunda, etc.
}

export const obterNomeDiaSemana = (diaSemana: number): string => {
  const dias = [
    'Domingo',
    'Segunda-feira',
    'Terça-feira',
    'Quarta-feira',
    'Quinta-feira',
    'Sexta-feira',
    'Sábado'
  ]
  return dias[diaSemana] || ''
}

export const obterProximaData = (diaSemana: number, hora: string): Date => {
  const hoje = new Date()
  const diasParaAdicionar = (diaSemana - hoje.getDay() + 7) % 7
  const proximaData = new Date(hoje)
  proximaData.setDate(hoje.getDate() + diasParaAdicionar)
  
  const [horas, minutos] = hora.split(':').map(Number)
  proximaData.setHours(horas, minutos, 0, 0)
  
  return proximaData
}

// Utilitários de status
export const obterCorStatus = (status: StatusConsulta): string => {
  const cores = {
    [StatusConsulta.AGENDADA]: '#3B82F6', // Azul
    [StatusConsulta.CONFIRMADA]: '#10B981', // Verde
    [StatusConsulta.EM_ANDAMENTO]: '#F59E0B', // Amarelo
    [StatusConsulta.CONCLUIDA]: '#059669', // Verde escuro
    [StatusConsulta.CANCELADA]: '#EF4444', // Vermelho
    [StatusConsulta.FALTOU]: '#6B7280' // Cinza
  }
  return cores[status] || '#6B7280'
}

export const obterTextoStatus = (status: StatusConsulta): string => {
  const textos = {
    [StatusConsulta.AGENDADA]: 'Agendada',
    [StatusConsulta.CONFIRMADA]: 'Confirmada',
    [StatusConsulta.EM_ANDAMENTO]: 'Em Andamento',
    [StatusConsulta.CONCLUIDA]: 'Concluída',
    [StatusConsulta.CANCELADA]: 'Cancelada',
    [StatusConsulta.FALTOU]: 'Paciente Faltou'
  }
  return textos[status] || status
}

export const obterTextoTipoUsuario = (tipo: TipoUsuario): string => {
  const textos = {
    [TipoUsuario.ADMINISTRADOR]: 'Administrador',
    [TipoUsuario.MEDICO]: 'Médico',
    [TipoUsuario.RECEPCIONISTA]: 'Recepcionista',
    [TipoUsuario.PACIENTE]: 'Paciente'
  }
  return textos[tipo] || tipo
}

// Utilitários de permissão
export const podeEditarConsulta = (
  tipoUsuario: TipoUsuario,
  statusConsulta: StatusConsulta
): boolean => {
  if (tipoUsuario === TipoUsuario.ADMINISTRADOR) return true
  
  if (tipoUsuario === TipoUsuario.RECEPCIONISTA) {
    return [StatusConsulta.AGENDADA, StatusConsulta.CONFIRMADA].includes(statusConsulta)
  }
  
  if (tipoUsuario === TipoUsuario.MEDICO) {
    return statusConsulta !== StatusConsulta.CANCELADA
  }
  
  return false
}

export const podeCancelarConsulta = (
  tipoUsuario: TipoUsuario,
  statusConsulta: StatusConsulta
): boolean => {
  if (tipoUsuario === TipoUsuario.ADMINISTRADOR) return true
  
  return [StatusConsulta.AGENDADA, StatusConsulta.CONFIRMADA].includes(statusConsulta)
}

// Utilitários de cálculo
export const calcularIdade = (dataNascimento: Date): number => {
  const hoje = new Date()
  const nascimento = new Date(dataNascimento)
  let idade = hoje.getFullYear() - nascimento.getFullYear()
  
  const mesAtual = hoje.getMonth()
  const mesNascimento = nascimento.getMonth()
  
  if (mesAtual < mesNascimento || 
      (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {
    idade--
  }
  
  return idade
}

export const calcularDuracaoConsulta = (inicio: Date, fim: Date): number => {
  return Math.round((fim.getTime() - inicio.getTime()) / (1000 * 60)) // em minutos
}

// Utilitários de busca e filtro
export const filtrarPorTexto = <T>(
  items: T[],
  texto: string,
  campos: (keyof T)[]
): T[] => {
  if (!texto.trim()) return items
  
  const textoLower = texto.toLowerCase()
  
  return items.filter(item =>
    campos.some(campo => {
      const valor = item[campo]
      return valor && 
             typeof valor === 'string' && 
             valor.toLowerCase().includes(textoLower)
    })
  )
}

export const ordenarPor = <T>(
  items: T[],
  campo: keyof T,
  ordem: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...items].sort((a, b) => {
    const valorA = a[campo]
    const valorB = b[campo]
    
    if (valorA < valorB) return ordem === 'asc' ? -1 : 1
    if (valorA > valorB) return ordem === 'asc' ? 1 : -1
    return 0
  })
}
