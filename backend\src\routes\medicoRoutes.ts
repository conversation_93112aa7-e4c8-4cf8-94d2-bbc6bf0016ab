import { Router } from 'express'
import { MedicoController } from '../controllers/medicoController'
import { 
  authenticateToken,
  requireAdmin,
  requireMedicoOrRecepcionista,
  requireMedico,
  canModifyUser,
  logAction,
  rateLimitByUser
} from '../middleware/authMiddleware'

const router = Router()
const medicoController = new MedicoController()

/**
 * @route POST /api/medicos
 * @desc Criar novo médico
 * @access Admin, Recepcionista
 */
router.post('/',
  authenticateToken,
  requireMedicoOrRecepcionista,
  rateLimitByUser(10, 60 * 60 * 1000), // 10 criações por hora
  logAction('CREATE_MEDICO'),
  medicoController.create
)

/**
 * @route GET /api/medicos
 * @desc Listar médicos com filtros
 * @access Admin, Recepcionista, Médico
 */
router.get('/',
  authenticateToken,
  requireMedicoOrRecepcionista,
  medicoController.findMany
)

/**
 * @route GET /api/medicos/me
 * @desc Buscar perfil do médico atual
 * @access Médico
 */
router.get('/me',
  authenticateToken,
  requireMedico,
  medicoController.findMyProfile
)

/**
 * @route PUT /api/medicos/me
 * @desc Atualizar perfil do médico atual
 * @access Médico
 */
router.put('/me',
  authenticateToken,
  requireMedico,
  logAction('UPDATE_MEDICO_PROFILE'),
  medicoController.updateMyProfile
)

/**
 * @route GET /api/medicos/me/stats
 * @desc Obter estatísticas do médico atual
 * @access Médico
 */
router.get('/me/stats',
  authenticateToken,
  requireMedico,
  medicoController.getMyStats
)

/**
 * @route GET /api/medicos/especialidade/:especialidadeId
 * @desc Buscar médicos por especialidade
 * @access Admin, Recepcionista, Médico
 */
router.get('/especialidade/:especialidadeId',
  authenticateToken,
  requireMedicoOrRecepcionista,
  medicoController.findByEspecialidade
)

/**
 * @route GET /api/medicos/:id
 * @desc Buscar médico por ID
 * @access Admin, Recepcionista, Médico
 */
router.get('/:id',
  authenticateToken,
  requireMedicoOrRecepcionista,
  medicoController.findById
)

/**
 * @route PUT /api/medicos/:id
 * @desc Atualizar médico
 * @access Admin, Recepcionista
 */
router.put('/:id',
  authenticateToken,
  requireMedicoOrRecepcionista,
  logAction('UPDATE_MEDICO'),
  medicoController.update
)

/**
 * @route GET /api/medicos/:id/stats
 * @desc Obter estatísticas do médico
 * @access Admin, Recepcionista
 */
router.get('/:id/stats',
  authenticateToken,
  requireMedicoOrRecepcionista,
  medicoController.getStats
)

/**
 * @route PATCH /api/medicos/:id/deactivate
 * @desc Desativar médico
 * @access Admin
 */
router.patch('/:id/deactivate',
  authenticateToken,
  requireAdmin,
  logAction('DEACTIVATE_MEDICO'),
  medicoController.deactivate
)

/**
 * @route PATCH /api/medicos/:id/activate
 * @desc Ativar médico
 * @access Admin
 */
router.patch('/:id/activate',
  authenticateToken,
  requireAdmin,
  logAction('ACTIVATE_MEDICO'),
  medicoController.activate
)

export default router
