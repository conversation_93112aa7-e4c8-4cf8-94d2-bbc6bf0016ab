{"name": "Lembretes Automáticos - Agenteia <PERSON>", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */2 * * *"}]}}, "id": "cron-lembre<PERSON>", "name": "Cron - A cada 2 horas", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  c.id,\n  c.dataHora,\n  c.status,\n  c.observacoes,\n  p.id as paciente_id,\n  up.nome as paciente_nome,\n  up.telefone as paciente_telefone,\n  up.email as paciente_email,\n  m.id as medico_id,\n  um.nome as medico_nome,\n  m.crm,\n  e.nome as especialidade,\n  e.valorConsulta\nFROM consultas c\nJOIN pacientes p ON c.pacienteId = p.id\nJOIN usuarios up ON p.usuarioId = up.id\nJOIN medicos m ON c.medicoId = m.id\nJOIN usuarios um ON m.usuarioId = um.id\nJOIN especialidades e ON c.especialidadeId = e.id\nWHERE \n  c.status IN ('AGENDADA', 'CONFIRMADA')\n  AND c.dataHora BETWEEN NOW() + INTERVAL '22 hours' AND NOW() + INTERVAL '26 hours'\n  AND NOT EXISTS (\n    SELECT 1 FROM lembretes_enviados le \n    WHERE le.consulta_id = c.id \n    AND le.tipo = 'lembrete_24h'\n    AND le.enviado_em > NOW() - INTERVAL '12 hours'\n  )\nORDER BY c.dataHora"}, "id": "buscar-consultas", "name": "Buscar Consultas 24h", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageValues": [{"role": "system", "content": "Crie uma mensagem de lembrete personalizada para consulta médica. A mensagem deve ser:\n\n1. AMIGÁVEL e PROFISSIONAL\n2. CONCISA (máximo 160 caracteres para SMS)\n3. INCLUIR informações essenciais\n4. TOM ACOLHEDOR\n\nFormato sugerido:\n🏥 Olá [Nome]! Lembrete: consulta amanhã às [hora] com Dr. [médico] ([especialidade]). Local: Agenteia Médicos. Dúvidas: (11) 3333-3333\n\nPersonalize baseado nos dados fornecidos."}, {"role": "user", "content": "=Paciente: {{ $json.paciente_nome }}\nMédico: Dr. {{ $json.medico_nome }}\nEspecialidade: {{ $json.especialidade }}\nData/Hora: {{ $json.dataHora }}\nValor: R$ {{ $json.valorConsulta }}\nObservações: {{ $json.observacoes || 'Nenhuma' }}"}]}}, "id": "gerar-mensagem-lembrete", "name": "IA - <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "tem-whatsapp", "leftValue": "={{ $json.paciente_telefone }}", "rightValue": "", "operator": {"type": "string", "operation": "exists"}}], "combinator": "and"}, "options": {}}, "id": "verificar-telefone", "name": "Tem WhatsApp?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v17.0/{{ $vars.WHATSAPP_PHONE_ID }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.WHATSAPP_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "body": "={\n  \"messaging_product\": \"whatsapp\",\n  \"to\": \"{{ $json.paciente_telefone.replace(/\\D/g, '') }}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"{{ $('gerar-mensagem-lembrete').item.json }}\"\n  }\n}"}, "id": "enviar-whatsapp-lembrete", "name": "Enviar Whats<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.paciente_email }}", "subject": "🏥 Lembrete: Sua consulta é amanhã!", "emailType": "html", "message": "=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Lembrete de Consulta</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0;\">\n            <h1 style=\"margin: 0; font-size: 24px;\">🏥 Agenteia Médicos</h1>\n            <p style=\"margin: 5px 0 0 0; opacity: 0.9;\">Lembrete de Consulta</p>\n        </div>\n        \n        <div style=\"background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">\n            <h2 style=\"color: #495057; margin-top: 0;\"><PERSON><PERSON><PERSON>, {{ $json.paciente_nome }}!</h2>\n            \n            <p>Este é um lembrete amigável sobre sua consulta marcada para <strong>amanhã</strong>:</p>\n            \n            <div style=\"background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;\">\n                <h3 style=\"margin-top: 0; color: #28a745;\">📅 Detalhes da Consulta</h3>\n                <p><strong>📍 Data/Hora:</strong> {{ $json.dataHora }}</p>\n                <p><strong>👨‍⚕️ Médico:</strong> Dr. {{ $json.medico_nome }}</p>\n                <p><strong>🏥 Especialidade:</strong> {{ $json.especialidade }}</p>\n                <p><strong>💰 Valor:</strong> R$ {{ $json.valorConsulta }}</p>\n            </div>\n            \n            <div style=\"background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;\">\n                <h4 style=\"margin-top: 0; color: #856404;\">📋 Orientações Importantes</h4>\n                <ul style=\"margin: 10px 0; padding-left: 20px;\">\n                    <li>Chegue 15 minutos antes do horário</li>\n                    <li>Traga documento com foto e cartão do plano</li>\n                    <li>Liste seus medicamentos atuais</li>\n                    <li>Prepare suas dúvidas para o médico</li>\n                </ul>\n            </div>\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <p style=\"margin: 0; color: #6c757d;\">📍 <strong>Endereço:</strong> Rua Principal, 1000 - Centro - São Paulo/SP</p>\n                <p style=\"margin: 5px 0; color: #6c757d;\">📞 <strong>Contato:</strong> (11) 3333-3333</p>\n            </div>\n            \n            <div style=\"background: #d1ecf1; padding: 15px; border-radius: 8px; text-align: center;\">\n                <p style=\"margin: 0; color: #0c5460;\">💬 <strong>Precisa remarcar?</strong> Entre em contato conosco!</p>\n            </div>\n        </div>\n        \n        <div style=\"text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;\">\n            <p>Este é um e-mail automático. Não responda a esta mensagem.</p>\n            <p>© 2025 Agenteia Médicos - Cuidando da sua saúde com tecnologia</p>\n        </div>\n    </div>\n</body>\n</html>"}, "id": "enviar-email-lembrete", "name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "lembretes_enviados", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"consulta_id": "={{ $json.id }}", "tipo": "lembrete_24h", "canal": "whatsapp_email", "telefone": "={{ $json.paciente_telefone }}", "email": "={{ $json.paciente_email }}", "mensagem": "={{ $('gerar-mensagem-lembrete').item.json }}", "enviado_em": "={{ $now }}", "status": "enviado"}, "matchingColumns": [], "schema": []}}, "id": "registrar-lembrete", "name": "Registrar <PERSON><PERSON><PERSON> Envia<PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  c.id,\n  c.dataHora,\n  p.id as paciente_id,\n  up.nome as paciente_nome,\n  up.telefone as paciente_telefone,\n  um.nome as medico_nome,\n  e.nome as especialidade\nFROM consultas c\nJOIN pacientes p ON c.pacienteId = p.id\nJOIN usuarios up ON p.usuarioId = up.id\nJOIN medicos m ON c.medicoId = m.id\nJOIN usuarios um ON m.usuarioId = um.id\nJOIN especialidades e ON c.especialidadeId = e.id\nWHERE \n  c.status IN ('AGENDADA', 'CONFIRMADA')\n  AND c.dataHora BETWEEN NOW() + INTERVAL '50 minutes' AND NOW() + INTERVAL '70 minutes'\n  AND NOT EXISTS (\n    SELECT 1 FROM lembretes_enviados le \n    WHERE le.consulta_id = c.id \n    AND le.tipo = 'lembrete_1h'\n    AND le.enviado_em > NOW() - INTERVAL '30 minutes'\n  )\nORDER BY c.dataHora"}, "id": "buscar-consultas-1h", "name": "Buscar Consultas 1h", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v17.0/{{ $vars.WHATSAPP_PHONE_ID }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.WHATSAPP_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "body": "={\n  \"messaging_product\": \"whatsapp\",\n  \"to\": \"{{ $json.paciente_telefone.replace(/\\D/g, '') }}\",\n  \"type\": \"text\",\n  \"text\": {\n    \"body\": \"🏥 Olá {{ $json.paciente_nome }}! Sua consulta com Dr. {{ $json.medico_nome }} ({{ $json.especialidade }}) é em 1 hora. Não se esqueça! 📍 Agenteia Médicos\"\n  }\n}"}, "id": "lembrete-1h", "name": "Lembrete 1h WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "lembretes_enviados", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"consulta_id": "={{ $json.id }}", "tipo": "lembrete_1h", "canal": "whatsapp", "telefone": "={{ $json.paciente_telefone }}", "mensagem": "Lembrete 1h antes da consulta", "enviado_em": "={{ $now }}", "status": "enviado"}, "matchingColumns": [], "schema": []}}, "id": "registrar-lembrete-1h", "name": "Registrar Le<PERSON><PERSON> 1h", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [900, 600]}], "connections": {"Cron - A cada 2 horas": {"main": [[{"node": "Buscar Consultas 24h", "type": "main", "index": 0}, {"node": "Buscar Consultas 1h", "type": "main", "index": 0}]]}, "Buscar Consultas 24h": {"main": [[{"node": "IA - <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "IA - Gerar Mensagem Lembrete": {"main": [[{"node": "Tem WhatsApp?", "type": "main", "index": 0}]]}, "Tem WhatsApp?": {"main": [[{"node": "Enviar Whats<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Enviar WhatsApp Lembrete": {"main": [[{"node": "Registrar <PERSON><PERSON><PERSON> Envia<PERSON>", "type": "main", "index": 0}]]}, "Enviar Email Lembrete": {"main": [[{"node": "Registrar <PERSON><PERSON><PERSON> Envia<PERSON>", "type": "main", "index": 0}]]}, "Buscar Consultas 1h": {"main": [[{"node": "Lembrete 1h WhatsApp", "type": "main", "index": 0}]]}, "Lembrete 1h WhatsApp": {"main": [[{"node": "Registrar Le<PERSON><PERSON> 1h", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Sao_Paulo"}, "versionId": "1"}