# Configurações do Banco de Dados
DATABASE_URL="postgresql://usuario:senha@localhost:5432/agenteia_medicos"

# Configurações do Supabase
NEXT_PUBLIC_SUPABASE_URL="sua_url_do_supabase"
NEXT_PUBLIC_SUPABASE_ANON_KEY="sua_chave_anonima_do_supabase"
SUPABASE_SERVICE_ROLE_KEY="sua_chave_de_servico_do_supabase"

# Configurações JWT
JWT_SECRET="seu_jwt_secret_muito_seguro"
JWT_EXPIRES_IN="7d"

# Configurações do Servidor
PORT=3001
NODE_ENV="development"

# Configurações de Email
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="sua_senha_de_app"

# Configurações de SMS (opcional)
TWILIO_ACCOUNT_SID="seu_account_sid"
TWILIO_AUTH_TOKEN="seu_auth_token"
TWILIO_PHONE_NUMBER="+*************"

# Configurações de Upload
UPLOAD_MAX_SIZE="10mb"
UPLOAD_ALLOWED_TYPES="image/jpeg,image/png,application/pdf"

# Configurações da Aplicação
APP_NAME="Agenteia Médicos"
APP_URL="http://localhost:3000"
API_URL="http://localhost:3001"

# Configurações de Notificações
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
NOTIFICATION_REMINDER_HOURS=24
